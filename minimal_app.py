"""
Minimal FastAPI application that works with current environment
"""

import pandas as pd
from pathlib import Path
import json

# Try to import FastAPI with fallback
try:
    from fastapi import FastAPI
    from fastapi.responses import HTMLResponse
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False

# If FastAPI is not available, create a simple HTTP server
if not FASTAPI_AVAILABLE:
    print("❌ FastAPI not available. Please install with: pip install fastapi uvicorn")
    exit(1)

# Create app
app = FastAPI(title="Replenishment Dashboard")

# Simple data storage
data_store = {}

def load_data():
    """Load data from parquet files"""
    try:
        data_dir = Path("data")
        files = list(data_dir.glob("CE_JDA_SRD_for_streamlit*"))
        
        for file_path in files:
            try:
                period = file_path.name.split('_')[-1]
                df = pd.read_parquet(file_path)
                df = df[df.division.notna()]
                data_store[period] = {
                    'rows': len(df),
                    'divisions': sorted(df['division'].unique().tolist()),
                    'countries': sorted(df['country'].unique().tolist()) if 'country' in df.columns else [],
                    'columns': df.columns.tolist()
                }
                print(f"✅ Loaded {period}: {len(df)} rows")
            except Exception as e:
                print(f"❌ Error loading {file_path}: {e}")
                
        return len(data_store) > 0
    except Exception as e:
        print(f"❌ Error in load_data: {e}")
        return False

@app.on_event("startup")
async def startup():
    """Load data on startup"""
    success = load_data()
    if success:
        print(f"🚀 Dashboard ready with periods: {list(data_store.keys())}")
    else:
        print("⚠️ No data loaded")

@app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint"""
    
    html_content = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Replenishment Dashboard</title>
        <style>
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
                color: #f8fafc;
                margin: 0;
                padding: 2rem;
                min-height: 100vh;
            }}
            .container {{
                max-width: 1200px;
                margin: 0 auto;
            }}
            .header {{
                text-align: center;
                margin-bottom: 3rem;
            }}
            .header h1 {{
                font-size: 3rem;
                background: linear-gradient(135deg, #60a5fa, #3b82f6);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin-bottom: 1rem;
            }}
            .status-card {{
                background: rgba(30, 41, 59, 0.8);
                border-radius: 1rem;
                padding: 2rem;
                border: 1px solid rgba(255, 255, 255, 0.1);
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
                margin-bottom: 2rem;
                backdrop-filter: blur(10px);
            }}
            .success {{ color: #10b981; }}
            .error {{ color: #ef4444; }}
            .stats-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem;
                margin-top: 2rem;
            }}
            .stat-card {{
                background: rgba(15, 23, 42, 0.8);
                border-radius: 0.75rem;
                padding: 1.5rem;
                border: 1px solid rgba(71, 85, 105, 0.3);
                text-align: center;
            }}
            .stat-card h3 {{
                color: #94a3b8;
                font-size: 0.875rem;
                margin-bottom: 0.5rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }}
            .stat-value {{
                font-size: 2rem;
                font-weight: 700;
                color: #3b82f6;
            }}
            .period-tags {{
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                margin-top: 1rem;
                justify-content: center;
            }}
            .period-tag {{
                background: #3b82f6;
                color: white;
                padding: 0.25rem 0.75rem;
                border-radius: 0.375rem;
                font-size: 0.875rem;
                font-weight: 500;
            }}
            .feature-list {{
                background: rgba(15, 23, 42, 0.8);
                border-radius: 1rem;
                padding: 2rem;
                border: 1px solid rgba(71, 85, 105, 0.3);
            }}
            .feature-list h2 {{
                color: #3b82f6;
                margin-bottom: 1rem;
            }}
            .feature-list ul {{
                list-style: none;
                padding: 0;
            }}
            .feature-list li {{
                padding: 0.5rem 0;
                border-bottom: 1px solid rgba(71, 85, 105, 0.2);
            }}
            .feature-list li:last-child {{
                border-bottom: none;
            }}
            .feature-list li::before {{
                content: "✅ ";
                margin-right: 0.5rem;
            }}
            .endpoints {{
                margin-top: 2rem;
                background: rgba(15, 23, 42, 0.6);
                border-radius: 0.75rem;
                padding: 1.5rem;
                border: 1px solid rgba(71, 85, 105, 0.2);
            }}
            .endpoints h3 {{
                color: #60a5fa;
                margin-bottom: 1rem;
            }}
            .endpoint {{
                background: rgba(30, 41, 59, 0.5);
                border-radius: 0.5rem;
                padding: 1rem;
                margin-bottom: 0.5rem;
                border: 1px solid rgba(71, 85, 105, 0.2);
            }}
            .endpoint code {{
                color: #fbbf24;
                font-weight: 600;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 FastAPI Dashboard</h1>
                <p>Modern Replenishment Analytics Platform</p>
            </div>
            
            <div class="status-card">
                <h2>📊 Application Status</h2>
                {"<p class='success'>✅ FastAPI application is running successfully!</p>" if data_store else "<p class='error'>❌ No data loaded</p>"}
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Periods Loaded</h3>
                        <div class="stat-value">{len(data_store)}</div>
                        <div class="period-tags">
                            {' '.join(f'<span class="period-tag">{period.upper()}</span>' for period in data_store.keys())}
                        </div>
                    </div>
                    
                    {"".join(f'''
                    <div class="stat-card">
                        <h3>{period.upper()} Data</h3>
                        <div class="stat-value">{data_store[period]["rows"]:,}</div>
                        <p style="color: #94a3b8; font-size: 0.875rem; margin-top: 0.5rem;">
                            {len(data_store[period]["divisions"])} divisions, {len(data_store[period]["countries"])} countries
                        </p>
                    </div>
                    ''' for period in list(data_store.keys())[:3])}
                </div>
            </div>
            
            <div class="feature-list">
                <h2>🎯 Implementation Status</h2>
                <ul>
                    <li>FastAPI backend application running</li>
                    <li>Data loading from parquet files</li>
                    <li>Modern dark theme interface</li>
                    <li>Responsive design layout</li>
                    <li>Health monitoring endpoints</li>
                    <li>JSON API responses</li>
                    <li>Error handling and logging</li>
                    <li>Ready for HTMX integration</li>
                </ul>
                
                <div class="endpoints">
                    <h3>🔗 Available Endpoints</h3>
                    <div class="endpoint">
                        <code>GET /</code> - This dashboard page
                    </div>
                    <div class="endpoint">
                        <code>GET /health</code> - Application health check
                    </div>
                    <div class="endpoint">
                        <code>GET /data</code> - Raw data summary
                    </div>
                    <div class="endpoint">
                        <code>GET /docs</code> - FastAPI automatic documentation
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """
    
    return html_content

@app.get("/health")
async def health():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "periods_loaded": len(data_store),
        "available_periods": list(data_store.keys()),
        "total_records": sum(data["rows"] for data in data_store.values())
    }

@app.get("/data")
async def get_data():
    """Get data summary"""
    return {
        "periods": data_store,
        "summary": {
            "total_periods": len(data_store),
            "total_records": sum(data["rows"] for data in data_store.values()),
            "all_divisions": list(set(div for data in data_store.values() for div in data["divisions"])),
            "all_countries": list(set(country for data in data_store.values() for country in data["countries"]))
        }
    }

if __name__ == "__main__":
    print("🚀 Starting minimal FastAPI dashboard...")
    print("📱 Dashboard will be available at: http://127.0.0.1:8000")
    print("📚 API docs at: http://127.0.0.1:8000/docs")
    print("🔍 Health check at: http://127.0.0.1:8000/health")
    print("\n⏹️  Press Ctrl+C to stop\n")
    
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
