"""
Working FastAPI Dashboard - Compatible with current environment
This version avoids the Pydantic compatibility issues
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
import locale
from typing import Optional, List, Dict, Any
from io import BytesIO

# Set locale for number formatting
try:
    locale.setlocale(locale.LC_ALL, '')
except:
    pass

# Simple data storage without Pydantic models
class SimpleDataLoader:
    def __init__(self):
        self.dataframes: Dict[str, pd.DataFrame] = {}
        self.data_dir = Path("data")
        
    def load_all_data(self) -> Dict[str, pd.DataFrame]:
        """Load all parquet files matching the pattern"""
        try:
            # Find all files containing "CE_JDA_SRD_for_streamlit" in the data folder
            matching_files = list(self.data_dir.glob("CE_JDA_SRD_for_streamlit*"))
            
            if not matching_files:
                print("Warning: No matching files found in the data directory.")
                return {}
            
            # Read each parquet file into a dataframe and organize by period
            for file_path in matching_files:
                try:
                    # Extract period from filename
                    period = file_path.name.split('_')[-1]
                    
                    df = pd.read_parquet(file_path)
                    df = df[df.division.notna()]
                    
                    # Add dataframe to dictionary with period as key
                    self.dataframes[period] = df
                    print(f"Loaded data for period {period}: {len(df)} rows")
                    
                except Exception as e:
                    print(f"Warning: Could not read file {file_path.name}: {str(e)}")
                    continue
                    
            if not self.dataframes:
                print("Error: No valid data files could be read.")
                return {}
                
            return self.dataframes
        
        except Exception as e:
            print(f"Error loading data: {str(e)}")
            return {}
    
    def get_dataframe(self, period: str) -> Optional[pd.DataFrame]:
        """Get dataframe for a specific period"""
        return self.dataframes.get(period)
    
    def get_available_periods(self) -> list:
        """Get list of available periods"""
        return list(self.dataframes.keys())
    
    def create_delisted_new_products_dfs(self, df1: pd.DataFrame, df2: pd.DataFrame):
        """Find products that are in previous period but not in next period, and vice versa."""
        # Create composite keys for comparison (country + tpnb)
        df1_copy = df1.copy()
        df2_copy = df2.copy()
        
        df1_copy['composite_key'] = df1_copy['country'] + '_' + df1_copy['tpnb'].astype(str)
        df2_copy['composite_key'] = df2_copy['country'] + '_' + df2_copy['tpnb'].astype(str)
        
        # Get sets of keys for each period
        df1_keys = set(df1_copy['composite_key'])
        df2_keys = set(df2_copy['composite_key'])
        
        # Find keys in df1 but not in df2 (delisted)
        keys_in_df1_not_in_df2 = df1_keys - df2_keys
        
        # Find keys in df2 but not in df1 (new)
        keys_in_df2_not_in_df1 = df2_keys - df1_keys
        
        # Filter the original dataframes
        delisted_products = df1_copy[df1_copy['composite_key'].isin(keys_in_df1_not_in_df2)]
        new_products = df2_copy[df2_copy['composite_key'].isin(keys_in_df2_not_in_df1)]
        
        # Drop the temporary composite key
        if not delisted_products.empty:
            delisted_products = delisted_products.drop('composite_key', axis=1)
        if not new_products.empty:
            new_products = new_products.drop('composite_key', axis=1)
        
        return delisted_products, new_products

# Global data loader instance
data_loader = SimpleDataLoader()

# Helper functions
def format_number(x):
    """Format number with space as thousands separator"""
    try:
        return locale.format_string("%d", x, grouping=True).replace(",", " ")
    except:
        return str(x)

def format_k(number):
    """Format numbers in K format"""
    if number >= 1000:
        return f"{number/1000:.1f}K"
    return str(number)

# Try to import FastAPI with fallback to simple HTTP server
try:
    from fastapi import FastAPI, Request, Query, HTTPException
    from fastapi.responses import HTMLResponse, JSONResponse, StreamingResponse
    from fastapi.staticfiles import StaticFiles
    from fastapi.templating import Jinja2Templates
    FASTAPI_AVAILABLE = True
except ImportError:
    print("❌ FastAPI not available. Please install with: pip install fastapi uvicorn")
    FASTAPI_AVAILABLE = False

if not FASTAPI_AVAILABLE:
    exit(1)

# Create FastAPI app without Pydantic models
app = FastAPI(
    title="Replenishment Types Dashboard",
    description="Modern dashboard for replenishment analysis",
    version="1.0.0"
)

# Mount static files (with error handling)
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except:
    print("Warning: Static files directory not found")

# Setup templates (with error handling)
try:
    templates = Jinja2Templates(directory="templates")
except:
    print("Warning: Templates directory not found")
    templates = None

@app.on_event("startup")
async def startup_event():
    """Load data on startup"""
    data_loader.load_all_data()
    periods = data_loader.get_available_periods()
    if periods:
        print(f"✅ Dashboard ready with periods: {periods}")
    else:
        print("⚠️ No data loaded")

@app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint"""
    periods = data_loader.get_available_periods()
    
    # Get sample data
    sample_data = {}
    if periods:
        df = data_loader.get_dataframe(periods[0])
        if df is not None:
            sample_data = {
                'total_rows': len(df),
                'divisions': sorted(df['division'].unique()) if 'division' in df.columns else [],
                'countries': sorted(df['country'].unique()) if 'country' in df.columns else []
            }
    
    # If templates are available, use them
    if templates:
        try:
            return templates.TemplateResponse(
                "working_dashboard.html",
                {
                    "request": {"url": {"path": "/"}},  # Simple request object
                    "title": "Replenishment Types Dashboard",
                    "periods": periods,
                    "sample_data": sample_data,
                    "countries": ["CZ", "HU", "SK"]
                }
            )
        except:
            pass
    
    # Fallback to inline HTML
    return HTMLResponse(content=create_dashboard_html(periods, sample_data))

def create_dashboard_html(periods, sample_data):
    """Create dashboard HTML content"""
    return f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Replenishment Types Dashboard</title>
        <script src="https://unpkg.com/htmx.org@1.9.10"></script>
        <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
        <style>
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
                color: #f8fafc;
                margin: 0;
                padding: 0;
                min-height: 100vh;
            }}
            .navbar {{
                background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
                border-bottom: 1px solid #475569;
                padding: 1rem 0;
                position: sticky;
                top: 0;
                z-index: 100;
            }}
            .nav-container {{
                max-width: 1400px;
                margin: 0 auto;
                padding: 0 1.5rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 1rem;
            }}
            .nav-brand h1 {{
                font-size: 1.875rem;
                font-weight: 700;
                background: linear-gradient(135deg, #60a5fa, #3b82f6);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin: 0;
            }}
            .nav-menu {{
                display: flex;
                align-items: center;
                gap: 1.5rem;
                flex-wrap: wrap;
            }}
            .period-selector {{
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }}
            .period-selector select {{
                background: #1e293b;
                border: 1px solid #475569;
                border-radius: 0.5rem;
                color: #f8fafc;
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }}
            .toggle-switch {{
                display: flex;
                align-items: center;
                gap: 0.5rem;
                cursor: pointer;
            }}
            .toggle-slider {{
                position: relative;
                width: 44px;
                height: 24px;
                background: #334155;
                border-radius: 12px;
                transition: all 0.3s ease;
                border: 1px solid #475569;
            }}
            .toggle-slider::before {{
                content: '';
                position: absolute;
                top: 2px;
                left: 2px;
                width: 18px;
                height: 18px;
                background: #f8fafc;
                border-radius: 50%;
                transition: all 0.3s ease;
            }}
            input[type="checkbox"]:checked + .toggle-slider {{
                background: #3b82f6;
                border-color: #3b82f6;
            }}
            input[type="checkbox"]:checked + .toggle-slider::before {{
                transform: translateX(20px);
            }}
            input[type="checkbox"] {{
                display: none;
            }}
            .main-content {{
                padding: 2rem;
                max-width: 1400px;
                margin: 0 auto;
            }}
            .dashboard-section {{
                background: linear-gradient(145deg, rgba(32, 32, 40, 0.8), rgba(10, 10, 15, 0.9));
                border-radius: 1.5rem;
                padding: 2rem;
                margin-bottom: 2rem;
                border: 1px solid rgba(255, 255, 255, 0.07);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.25);
            }}
            .dashboard-section h2 {{
                font-size: 1.875rem;
                font-weight: 700;
                background: linear-gradient(135deg, #60a5fa, #3b82f6);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin-bottom: 1.5rem;
            }}
            .stats-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem;
                margin-top: 2rem;
            }}
            .stat-card {{
                background: #1e293b;
                border-radius: 0.75rem;
                padding: 1.5rem;
                border: 1px solid #475569;
                text-align: center;
                transition: all 0.3s ease;
            }}
            .stat-card:hover {{
                transform: translateY(-2px);
                box-shadow: 0 10px 15px rgba(0, 0, 0, 0.3);
                border-color: #3b82f6;
            }}
            .stat-card h3 {{
                font-size: 0.875rem;
                color: #94a3b8;
                margin-bottom: 0.5rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }}
            .stat-value {{
                font-size: 1.5rem;
                font-weight: 700;
                color: #3b82f6;
            }}
            .loading-indicator {{
                display: none;
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: #1e293b;
                border: 1px solid #475569;
                border-radius: 0.75rem;
                padding: 2rem;
                z-index: 1000;
                box-shadow: 0 20px 25px rgba(0, 0, 0, 0.3);
            }}
            .spinner {{
                width: 40px;
                height: 40px;
                border: 3px solid #334155;
                border-top: 3px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 1rem;
            }}
            @keyframes spin {{
                0% {{ transform: rotate(0deg); }}
                100% {{ transform: rotate(360deg); }}
            }}
            .section-content {{
                min-height: 200px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #94a3b8;
            }}
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <h1>Replenishment Types Dashboard</h1>
                </div>
                
                <div class="nav-menu">
                    <div class="period-selector">
                        <label for="period-select">Period:</label>
                        <select id="period-select" 
                                hx-get="/api/period-data" 
                                hx-target="#main-content"
                                hx-trigger="change"
                                hx-include="[name='own_brand_filter']">
                            {' '.join(f'<option value="{period}">{period.upper()}</option>' for period in periods)}
                        </select>
                    </div>
                    
                    <div class="toggle-switch">
                        <input type="checkbox" 
                               name="own_brand_filter" 
                               id="own-brand-toggle"
                               hx-get="/api/period-data"
                               hx-target="#main-content"
                               hx-trigger="change"
                               hx-include="#period-select">
                        <span class="toggle-slider"></span>
                        <span>OwnBrand Only</span>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div id="main-content">
                <!-- Welcome Section -->
                <section class="dashboard-section">
                    <h2>Welcome to Replenishment Types Dashboard</h2>
                    <p>Modern FastAPI + HTMX dashboard for replenishment analysis</p>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <h3>Periods Available</h3>
                            <div class="stat-value">{len(periods)}</div>
                        </div>
                        <div class="stat-card">
                            <h3>Total Records</h3>
                            <div class="stat-value">{sample_data.get('total_rows', 0):,}</div>
                        </div>
                        <div class="stat-card">
                            <h3>Divisions</h3>
                            <div class="stat-value">{len(sample_data.get('divisions', []))}</div>
                        </div>
                        <div class="stat-card">
                            <h3>Countries</h3>
                            <div class="stat-value">{len(sample_data.get('countries', []))}</div>
                        </div>
                    </div>
                </section>

                <!-- Division Comparison Section -->
                <section class="dashboard-section">
                    <h2>Division TPN Numbers</h2>
                    <div id="division-section" 
                         hx-get="/api/division-comparison"
                         hx-trigger="load"
                         hx-include="[name='own_brand_filter'], #period-select">
                        <div class="section-content">
                            <div>
                                <div class="spinner"></div>
                                <p>Loading division comparison...</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- More sections will be loaded via HTMX -->
            </div>
        </main>

        <!-- Loading indicator -->
        <div id="loading-indicator" class="loading-indicator">
            <div class="spinner"></div>
            <span>Loading...</span>
        </div>

        <script>
            // Show loading indicator during HTMX requests
            document.body.addEventListener('htmx:beforeRequest', function(evt) {{
                document.getElementById('loading-indicator').style.display = 'block';
            }});
            
            document.body.addEventListener('htmx:afterRequest', function(evt) {{
                document.getElementById('loading-indicator').style.display = 'none';
            }});
        </script>
    </body>
    </html>
    """

@app.get("/health")
async def health():
    """Health check endpoint"""
    periods = data_loader.get_available_periods()
    return {
        "status": "healthy",
        "periods_loaded": len(periods),
        "available_periods": periods,
        "message": "FastAPI Dashboard is running successfully!"
    }

@app.get("/api/division-comparison", response_class=HTMLResponse)
async def division_comparison(
    period1: str = Query("25p5"),
    period2: str = Query("25p6"),
    own_brand: bool = Query(False, alias="own_brand_filter"),
    country: Optional[str] = Query(None)
):
    """Get division comparison data and chart"""
    
    df1 = data_loader.get_dataframe(period1)
    df2 = data_loader.get_dataframe(period2)
    
    if df1 is None or df2 is None:
        return HTMLResponse("<p>Period data not found</p>")
    
    # Apply filters
    if own_brand:
        df1 = df1[df1['own_brand'] == 'Y']
        df2 = df2[df2['own_brand'] == 'Y']
    
    if country:
        df1 = df1[df1['country'] == country]
        df2 = df2[df2['country'] == country]
    
    # Calculate counts for each division
    df1_counts = df1.groupby('division')['tpnb'].nunique().reset_index()
    df2_counts = df2.groupby('division')['tpnb'].nunique().reset_index()
    
    # Merge the counts to calculate differences
    merged_counts = pd.merge(df1_counts, df2_counts, on='division', suffixes=('_1', '_2'))
    merged_counts['difference'] = merged_counts['tpnb_2'] - merged_counts['tpnb_1']
    merged_counts['pct_change'] = ((merged_counts['tpnb_2'] - merged_counts['tpnb_1']) / merged_counts['tpnb_1'] * 100).round(1)
    
    # Filter out divisions with no difference and sort
    merged_counts = merged_counts[merged_counts['difference'] != 0].copy()
    merged_counts = merged_counts.sort_values('tpnb_2', ascending=False)
    
    # Create simple HTML response
    html_content = f"""
    <div style="background: #1e293b; border-radius: 0.75rem; padding: 1.5rem; border: 1px solid #475569;">
        <h3 style="color: #3b82f6; margin-bottom: 1rem;">Division Comparison: {period1.upper()} vs {period2.upper()}</h3>
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse; color: #f8fafc;">
                <thead>
                    <tr style="background: #334155;">
                        <th style="padding: 0.75rem; text-align: left; border: 1px solid #475569;">Division</th>
                        <th style="padding: 0.75rem; text-align: right; border: 1px solid #475569;">{period1.upper()}</th>
                        <th style="padding: 0.75rem; text-align: right; border: 1px solid #475569;">{period2.upper()}</th>
                        <th style="padding: 0.75rem; text-align: right; border: 1px solid #475569;">Difference</th>
                        <th style="padding: 0.75rem; text-align: right; border: 1px solid #475569;">Change %</th>
                    </tr>
                </thead>
                <tbody>
    """
    
    for _, row in merged_counts.iterrows():
        color = "#10b981" if row['difference'] > 0 else "#ef4444"
        arrow = "↑" if row['difference'] > 0 else "↓"
        html_content += f"""
                    <tr style="border-bottom: 1px solid #475569;">
                        <td style="padding: 0.75rem; border: 1px solid #475569;">{row['division']}</td>
                        <td style="padding: 0.75rem; text-align: right; border: 1px solid #475569;">{format_number(row['tpnb_1'])}</td>
                        <td style="padding: 0.75rem; text-align: right; border: 1px solid #475569;">{format_number(row['tpnb_2'])}</td>
                        <td style="padding: 0.75rem; text-align: right; border: 1px solid #475569; color: {color};">
                            {arrow} {format_number(abs(row['difference']))}
                        </td>
                        <td style="padding: 0.75rem; text-align: right; border: 1px solid #475569; color: {color};">
                            {row['pct_change']:+.1f}%
                        </td>
                    </tr>
        """
    
    html_content += """
                </tbody>
            </table>
        </div>
    </div>
    """
    
    return HTMLResponse(html_content)

if __name__ == "__main__":
    print("🚀 Starting Working FastAPI Dashboard...")
    print("📱 Dashboard will be available at: http://127.0.0.1:8000")
    print("📚 API docs at: http://127.0.0.1:8000/docs")
    print("🔍 Health check at: http://127.0.0.1:8000/health")
    print("\n⏹️  Press Ctrl+C to stop\n")
    
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
