<div class="chart-container">
    <div class="chart-section">
        <div class="chart-wrapper">
            <div id="division-chart" class="plotly-chart"></div>
        </div>
        
        <div class="table-section">
            <h3>Division-wise TPNB Changes</h3>
            <div class="data-table-wrapper">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Division</th>
                            <th>{{ period1.upper() }}</th>
                            <th>{{ period2.upper() }}</th>
                            <th>Difference</th>
                            <th>Change %</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for row in table_data %}
                        <tr>
                            <td>{{ row.division }}</td>
                            <td>{{ row.period1 }}</td>
                            <td>{{ row.period2 }}</td>
                            <td class="{% if row.difference > 0 %}positive{% else %}negative{% endif %}">
                                {% if row.difference > 0 %}↑{% else %}↓{% endif %} {{ "{:,}".format(row.difference|abs) }}
                            </td>
                            <td class="{% if row.pct_change > 0 %}positive{% else %}negative{% endif %}">
                                {{ "{:+.1f}".format(row.pct_change) }}%
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Country-level details expander -->
    <div class="expander-section">
        <button class="expander-toggle" onclick="toggleExpander('country-details')">
            <span class="expander-icon">▼</span>
            View Country-Level Details
        </button>
        
        <div id="country-details" class="expander-content" style="display: none;">
            {% for country in ["CZ", "HU", "SK"] %}
            <div class="country-section">
                <h4>{{ country }}</h4>
                <div hx-get="/api/division-comparison?country={{ country }}"
                     hx-trigger="revealed"
                     hx-include="[name='own_brand_filter'], #period-select">
                    <div class="loading-placeholder">
                        <div class="spinner-small"></div>
                        <span>Loading {{ country }} data...</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
    // Render the Plotly chart
    {% if chart_json %}
    const chartData = {{ chart_json|safe }};
    Plotly.newPlot('division-chart', chartData.data, chartData.layout, {
        responsive: true,
        displayModeBar: false
    });
    {% endif %}
    
    function toggleExpander(id) {
        const content = document.getElementById(id);
        const toggle = content.previousElementSibling;
        const icon = toggle.querySelector('.expander-icon');
        
        if (content.style.display === 'none') {
            content.style.display = 'block';
            icon.textContent = '▲';
            // Trigger HTMX load for country details
            htmx.trigger(content, 'revealed');
        } else {
            content.style.display = 'none';
            icon.textContent = '▼';
        }
    }
</script>
