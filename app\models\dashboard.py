"""
Pydantic models for dashboard data
"""

from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from enum import Enum

class ReplenishmentType(str, Enum):
    SRP = "srp"
    NSRP = "nsrp"
    MU = "mu"
    FULL_PALLET = "full_pallet"
    SPLIT_PALLET = "split_pallet"

class Country(str, Enum):
    CZ = "CZ"
    HU = "HU"
    SK = "SK"

class Period(str, Enum):
    P1 = "25p1"
    P2 = "25p2"
    P3 = "25p3"
    P4 = "25p4"
    P5 = "25p5"
    P6 = "25p6"

class FilterRequest(BaseModel):
    """Request model for filtering data"""
    period: Optional[str] = "25p6"
    own_brand_only: bool = False
    divisions: Optional[List[str]] = None
    departments: Optional[List[str]] = None
    countries: Optional[List[str]] = None

class DivisionComparison(BaseModel):
    """Model for division comparison data"""
    division: str
    period1_count: int
    period2_count: int
    difference: int
    pct_change: float

class ReplenishmentStats(BaseModel):
    """Model for replenishment statistics"""
    division: str
    department: Optional[str] = None
    country: Optional[str] = None
    srp_diff_pct: float
    nsrp_diff_pct: float
    mu_diff_pct: float
    full_pallet_diff_pct: float
    split_pallet_diff_pct: float

class ProductData(BaseModel):
    """Model for product data"""
    tpnb: str
    division: str
    department: str
    product_name: str
    supplier_name: Optional[str] = None
    country: str
    own_brand: str
    srp: int
    nsrp: int
    mu: int
    full_pallet: int
    split_pallet: int

class ChartData(BaseModel):
    """Model for chart data"""
    chart_type: str
    data: Dict[str, Any]
    layout: Dict[str, Any]
