"""
Startup script for the FastAPI + HTMX Dashboard
"""

import uvicorn
import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """Run the dashboard application"""
    
    # Check if data directory exists
    data_dir = Path("data")
    if not data_dir.exists():
        print("❌ Data directory not found!")
        print("Please ensure the 'data' directory exists with parquet files.")
        return
    
    # Check for data files
    data_files = list(data_dir.glob("CE_JDA_SRD_for_streamlit*"))
    if not data_files:
        print("❌ No data files found!")
        print("Please ensure parquet files are in the 'data' directory.")
        return
    
    print(f"✅ Found {len(data_files)} data files")
    for file in data_files:
        print(f"   📄 {file.name}")
    
    print("\n🚀 Starting FastAPI + HTMX Dashboard...")
    print("📱 Dashboard will be available at: http://127.0.0.1:8000")
    print("🔄 Hot reload enabled - changes will auto-refresh")
    print("\n⏹️  Press Ctrl+C to stop the server\n")
    
    try:
        uvicorn.run(
            "main:app",
            host="127.0.0.1",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Dashboard stopped!")
    except Exception as e:
        print(f"\n❌ Error starting dashboard: {e}")
        print("\nTroubleshooting:")
        print("1. Install requirements: pip install -r requirements_fastapi.txt")
        print("2. Check data directory exists with parquet files")
        print("3. Ensure no other application is using port 8000")

if __name__ == "__main__":
    main()
