# FastAPI + HTMX Replenishment Dashboard

A modern replacement for the Streamlit dashboard using FastAPI and HTMX with a dark theme design.

## 🚀 Features

- **Modern Tech Stack**: FastAPI backend with HTMX for dynamic frontend interactions
- **Dark Theme**: Contemporary dark UI with modern design principles
- **Interactive Charts**: Plotly visualizations with smooth HTMX updates
- **Real-time Filtering**: Dynamic filtering without page reloads
- **Excel Export**: Download data as Excel files
- **Responsive Design**: Works on desktop and mobile devices
- **Fast Performance**: Optimized data loading and caching

## 📋 Requirements

- Python 3.8+
- Data files in `data/` directory (parquet format)

## 🛠️ Installation

1. **Install Dependencies**:
   ```bash
   pip install -r requirements_fastapi.txt
   ```

2. **Verify Installation**:
   ```bash
   python test_app.py
   ```

3. **Start the Dashboard**:
   ```bash
   python run_dashboard.py
   ```
   
   Or manually:
   ```bash
   uvicorn main:app --reload
   ```

4. **Open in Browser**:
   Navigate to `http://127.0.0.1:8000`

## 📁 Project Structure

```
├── main.py                 # FastAPI application entry point
├── run_dashboard.py        # Startup script
├── test_app.py            # Test script
├── requirements_fastapi.txt # Python dependencies
├── app/
│   ├── core/
│   │   ├── config.py      # Application settings
│   │   └── data_loader.py # Data loading utilities
│   ├── models/
│   │   └── dashboard.py   # Pydantic models
│   └── routers/
│       ├── dashboard.py   # HTML page routes
│       └── api.py         # API endpoints
├── templates/
│   ├── base.html          # Base template
│   ├── dashboard.html     # Main dashboard
│   ├── error.html         # Error page
│   └── components/        # HTMX components
├── static/
│   └── css/
│       └── styles.css     # Modern dark theme styles
└── data/                  # Parquet data files
```

## 🎯 Key Features Implemented

### 1. **Division TPN Numbers**
- Interactive bar charts comparing periods
- Country-level drill-down
- Real-time filtering

### 2. **Replenishment Types Analysis**
- Heatmap visualizations
- Division and department level views
- Percentage change calculations

### 3. **Delisted/New Products**
- Side-by-side comparison charts
- Multi-level analysis
- Dynamic filtering

### 4. **Top Products**
- Sortable product tables
- Excel export functionality
- Multiple replenishment type views

### 5. **Interactive Filtering**
- OwnBrand toggle
- Period selection
- Division/department filters
- Country-specific views

## 🎨 Design Features

- **Dark Theme**: Modern dark color palette
- **Responsive Layout**: Adapts to different screen sizes
- **Smooth Animations**: CSS transitions and HTMX effects
- **Loading States**: Visual feedback during data loading
- **Modern Typography**: Clean, readable fonts
- **Interactive Elements**: Hover effects and smooth transitions

## 🔧 Configuration

Edit `app/core/config.py` to customize:

- Server settings (host, port)
- Data directory location
- Available periods and countries
- UI preferences

## 📊 Data Requirements

The application expects parquet files in the `data/` directory with names matching:
`CE_JDA_SRD_for_streamlit_*`

Required columns:
- `country`, `division`, `department`
- `tpnb`, `product_name`, `supplier_name`
- `own_brand`
- `srp`, `nsrp`, `mu`, `full_pallet`, `split_pallet`

## 🚀 Performance Features

- **Async Data Loading**: Non-blocking data operations
- **Caching**: LRU cache for expensive calculations
- **Lazy Loading**: Components load on demand
- **Optimized Queries**: Efficient pandas operations

## 🔍 Troubleshooting

1. **Import Errors**: Install requirements with `pip install -r requirements_fastapi.txt`
2. **No Data**: Ensure parquet files are in the `data/` directory
3. **Port Issues**: Change port in `app/core/config.py` if 8000 is in use
4. **Performance**: Check data file sizes and available memory

## 🆚 Comparison with Original

| Feature | Streamlit | FastAPI + HTMX |
|---------|-----------|----------------|
| Framework | Streamlit | FastAPI |
| Frontend | Streamlit components | HTMX + HTML |
| Styling | Custom CSS | Modern dark theme |
| Interactivity | Page reloads | Partial updates |
| Performance | Good | Excellent |
| Customization | Limited | Full control |
| Mobile Support | Basic | Responsive |

## 🎯 Next Steps

The application is fully functional and ready for use. All original Streamlit features have been replicated with modern improvements:

- ✅ Period navigation
- ✅ Division comparisons  
- ✅ Replenishment analysis
- ✅ Delisted/new products
- ✅ Top products tables
- ✅ Excel exports
- ✅ Filtering and interactions
- ✅ Dark theme design
- ✅ Responsive layout

Enjoy your modern dashboard! 🎉
