<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ title }}{% endblock %}</title>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Plotly -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/styles.css">
    
    {% block head %}{% endblock %}
</head>
<body class="dark-theme">
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h1>Replenishment Types Dashboard</h1>
            </div>
            
            <div class="nav-menu">
                <div class="period-selector">
                    <label for="period-select">Period:</label>
                    <select id="period-select" 
                            hx-get="/period/{value}" 
                            hx-target="#main-content"
                            hx-trigger="change"
                            hx-include="[name='own_brand_filter']">
                        {% for period in periods %}
                        <option value="{{ period.lower().replace('_', '') }}" 
                                {% if current_period == period.lower().replace('_', '') %}selected{% endif %}>
                            {{ period }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="filter-controls">
                    <label class="toggle-switch">
                        <input type="checkbox" 
                               name="own_brand_filter" 
                               id="own-brand-toggle"
                               hx-get="/period/{{ current_period }}"
                               hx-target="#main-content"
                               hx-trigger="change"
                               hx-include="#period-select">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label">OwnBrand Only</span>
                    </label>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar Navigation -->
    <aside class="sidebar">
        <div class="sidebar-content">
            <h3>Navigation</h3>
            <ul class="nav-links">
                <li>
                    <a href="#division" 
                       hx-get="/api/division-comparison"
                       hx-target="#section-division"
                       hx-include="[name='own_brand_filter'], #period-select"
                       class="nav-link">
                        Division TPN Numbers
                    </a>
                </li>
                <li>
                    <a href="#replenishment" 
                       hx-get="/api/replenishment-stats"
                       hx-target="#section-replenishment"
                       hx-include="[name='own_brand_filter'], #period-select"
                       class="nav-link">
                        Repl Types Changes %
                    </a>
                </li>
                <li>
                    <a href="#delisted" 
                       hx-get="/api/delisted-new-products"
                       hx-target="#section-delisted"
                       hx-include="[name='own_brand_filter'], #period-select"
                       class="nav-link">
                        Delisted/New
                    </a>
                </li>
                <li>
                    <a href="#topproducts" 
                       hx-get="/api/top-products"
                       hx-target="#section-topproducts"
                       hx-include="[name='own_brand_filter'], #period-select"
                       class="nav-link">
                        New Products by Repl Types
                    </a>
                </li>
            </ul>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <div id="main-content">
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Loading indicator -->
    <div id="loading-indicator" class="loading-indicator" style="display: none;">
        <div class="spinner"></div>
        <span>Loading...</span>
    </div>

    <script>
        // Show loading indicator during HTMX requests
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            document.getElementById('loading-indicator').style.display = 'flex';
        });
        
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            document.getElementById('loading-indicator').style.display = 'none';
        });
        
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
