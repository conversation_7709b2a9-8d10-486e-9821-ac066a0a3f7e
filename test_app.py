"""
Test script to verify the FastAPI application works
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from main import app
    from app.core.data_loader import data_loader
    from app.core.config import settings
    print("✅ All imports successful!")
    
    # Test data loading
    print(f"📁 Data directory: {settings.DATA_DIR}")
    print(f"🔧 Available periods: {settings.PERIODS}")
    print(f"🌍 Countries: {settings.COUNTRIES}")
    print(f"📊 Replenishment types: {settings.REPLENISHMENT_TYPES}")
    
    print("\n🚀 Application is ready to run!")
    print("Run with: uvicorn main:app --reload")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install the required packages:")
    print("pip install -r requirements_fastapi.txt")
    
except Exception as e:
    print(f"❌ Error: {e}")
