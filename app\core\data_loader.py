"""
Data loading utilities for the dashboard
Replicates functionality from utils.py
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, <PERSON><PERSON>, Optional
import asyncio
from functools import lru_cache

from app.core.config import settings

class DataLoader:
    """Data loader for parquet files"""
    
    def __init__(self):
        self.dataframes: Dict[str, pd.DataFrame] = {}
        self.data_dir = Path(settings.DATA_DIR)
        
    async def load_all_data(self) -> Dict[str, pd.DataFrame]:
        """Load all parquet files matching the pattern"""
        try:
            # Find all files containing "CE_JDA_SRD_for_streamlit" in the data folder
            matching_files = list(self.data_dir.glob("CE_JDA_SRD_for_streamlit*"))
            
            if not matching_files:
                print("Warning: No matching files found in the data directory.")
                return {}
            
            # Read each parquet file into a dataframe and organize by period
            for file_path in matching_files:
                try:
                    # Extract period from filename (e.g., "CE_JDA_SRD_for_streamlit_25p1" -> "25p1")
                    period = file_path.name.split('_')[-1]
                    
                    df = pd.read_parquet(file_path)
                    df = df[df.division.notna()]
                    
                    # Add dataframe to dictionary with period as key
                    self.dataframes[period] = df
                    print(f"Loaded data for period {period}: {len(df)} rows")
                    
                except Exception as e:
                    print(f"Warning: Could not read file {file_path.name}: {str(e)}")
                    continue
                    
            if not self.dataframes:
                print("Error: No valid data files could be read.")
                return {}
                
            return self.dataframes
        
        except Exception as e:
            print(f"Error loading data: {str(e)}")
            return {}
    
    def get_dataframe(self, period: str) -> Optional[pd.DataFrame]:
        """Get dataframe for a specific period"""
        return self.dataframes.get(period)
    
    def get_available_periods(self) -> list:
        """Get list of available periods"""
        return list(self.dataframes.keys())
    
    def create_delisted_new_products_dfs(self, df1: pd.DataFrame, df2: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Find products that are in previous period but not in next period, and vice versa.
        
        Parameters:
        df1 (pandas.DataFrame): DataFrame 1 (previous period)
        df2 (pandas.DataFrame): DataFrame 2 (current period)
        
        Returns:
        tuple: (delisted_products, new_products)
        """
        # Create composite keys for comparison (country + tpnb)
        df1_copy = df1.copy()
        df2_copy = df2.copy()
        
        df1_copy['composite_key'] = df1_copy['country'] + '_' + df1_copy['tpnb'].astype(str)
        df2_copy['composite_key'] = df2_copy['country'] + '_' + df2_copy['tpnb'].astype(str)
        
        # Get sets of keys for each period
        df1_keys = set(df1_copy['composite_key'])
        df2_keys = set(df2_copy['composite_key'])
        
        # Find keys in df1 but not in df2 (delisted)
        keys_in_df1_not_in_df2 = df1_keys - df2_keys
        
        # Find keys in df2 but not in df1 (new)
        keys_in_df2_not_in_df1 = df2_keys - df1_keys
        
        # Filter the original dataframes
        delisted_products = df1_copy[df1_copy['composite_key'].isin(keys_in_df1_not_in_df2)]
        new_products = df2_copy[df2_copy['composite_key'].isin(keys_in_df2_not_in_df1)]
        
        # Drop the temporary composite key
        if not delisted_products.empty:
            delisted_products = delisted_products.drop('composite_key', axis=1)
        if not new_products.empty:
            new_products = new_products.drop('composite_key', axis=1)
        
        return delisted_products, new_products
    
    @lru_cache(maxsize=32)
    def calculate_repl_type_stats(self, period1: str, period2: str, own_brand_only: bool = False) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        Calculate replenishment type statistics between two periods
        
        Returns:
        tuple: (country_dept_merged, total_merged, division_merged)
        """
        df1 = self.get_dataframe(period1)
        df2 = self.get_dataframe(period2)
        
        if df1 is None or df2 is None:
            return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
        
        # Apply OwnBrand filter if needed
        if own_brand_only:
            df1 = df1[df1['own_brand'] == 'Y']
            df2 = df2[df2['own_brand'] == 'Y']
        
        repl_types = settings.REPLENISHMENT_TYPES
        
        # Country, division, department level
        df1_group = df1.groupby(['country', 'division', 'department'], as_index=False)[repl_types].sum()
        df2_group = df2.groupby(['country', 'division', 'department'], as_index=False)[repl_types].sum()
        
        # Division, department level (no country)
        df1_group_total = df1.groupby(['division', 'department'], as_index=False)[repl_types].sum()
        df2_group_total = df2.groupby(['division', 'department'], as_index=False)[repl_types].sum()
        
        # Division level only
        df1_group_division = df1.groupby(['division'], as_index=False)[repl_types].sum()
        df2_group_division = df2.groupby(['division'], as_index=False)[repl_types].sum()
        
        # Merge dataframes with suffixes
        df_merged = pd.merge(df1_group, df2_group, on=['country', 'division', 'department'], how='outer', suffixes=('_1', '_2'))
        df_merged_total = pd.merge(df1_group_total, df2_group_total, on=['division', 'department'], how='outer', suffixes=('_1', '_2'))
        df_merged_division = pd.merge(df1_group_division, df2_group_division, on=['division'], how='outer', suffixes=('_1', '_2'))
        
        # Calculate percentage differences for each level
        for col in repl_types:
            df_merged[f'{col}_diff_pct'] = ((df_merged[f'{col}_2'] - df_merged[f'{col}_1']) / df_merged[f'{col}_1'] * 100).fillna(0).round(2)
            df_merged_total[f'{col}_diff_pct'] = ((df_merged_total[f'{col}_2'] - df_merged_total[f'{col}_1']) / df_merged_total[f'{col}_1'] * 100).fillna(0).round(2)
            df_merged_division[f'{col}_diff_pct'] = ((df_merged_division[f'{col}_2'] - df_merged_division[f'{col}_1']) / df_merged_division[f'{col}_1'] * 100).fillna(0).round(2)
        
        # Replace infinity values with 0
        df_merged.replace(np.inf, 0, inplace=True)
        df_merged_total.replace(np.inf, 0, inplace=True)
        df_merged_division.replace(np.inf, 0, inplace=True)
        
        return df_merged, df_merged_total, df_merged_division

# Global data loader instance
data_loader = DataLoader()
