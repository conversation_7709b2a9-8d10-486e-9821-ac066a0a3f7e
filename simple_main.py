"""
Simple FastAPI application to test basic functionality
"""

from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import pandas as pd
from pathlib import Path

# Create FastAPI app
app = FastAPI(title="Replenishment Dashboard")

# Mount static files
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except:
    pass

# Setup templates
templates = Jinja2Templates(directory="templates")

# Simple data loader
class SimpleDataLoader:
    def __init__(self):
        self.dataframes = {}
        
    def load_data(self):
        """Load data from parquet files"""
        try:
            data_dir = Path("data")
            files = list(data_dir.glob("CE_JDA_SRD_for_streamlit*"))
            
            for file_path in files:
                try:
                    period = file_path.name.split('_')[-1]
                    df = pd.read_parquet(file_path)
                    df = df[df.division.notna()]
                    self.dataframes[period] = df
                    print(f"Loaded {period}: {len(df)} rows")
                except Exception as e:
                    print(f"Error loading {file_path}: {e}")
                    
            return len(self.dataframes) > 0
        except Exception as e:
            print(f"Error in load_data: {e}")
            return False
    
    def get_periods(self):
        return list(self.dataframes.keys())
    
    def get_dataframe(self, period):
        return self.dataframes.get(period)

# Global data loader
data_loader = SimpleDataLoader()

@app.on_event("startup")
async def startup():
    """Load data on startup"""
    success = data_loader.load_data()
    if success:
        print(f"✅ Loaded data for periods: {data_loader.get_periods()}")
    else:
        print("❌ Failed to load data")

@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Root endpoint"""
    periods = data_loader.get_periods()
    
    # Get sample data
    sample_data = {}
    if periods:
        df = data_loader.get_dataframe(periods[0])
        if df is not None:
            sample_data = {
                'total_rows': len(df),
                'divisions': sorted(df['division'].unique()) if 'division' in df.columns else [],
                'countries': sorted(df['country'].unique()) if 'country' in df.columns else []
            }
    
    return templates.TemplateResponse(
        "simple_dashboard.html",
        {
            "request": request,
            "title": "Replenishment Dashboard",
            "periods": periods,
            "sample_data": sample_data
        }
    )

@app.get("/health")
async def health():
    """Health check endpoint"""
    return {
        "status": "ok",
        "periods_loaded": len(data_loader.get_periods()),
        "available_periods": data_loader.get_periods()
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
