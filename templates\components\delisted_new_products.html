<div class="delisted-new-container">
    <div class="products-grid">
        <!-- Delisted Products -->
        <div class="product-section">
            <h4>Delisted Products</h4>
            <div class="chart-wrapper">
                {% if delisted_chart %}
                <div id="delisted-chart" class="plotly-chart"></div>
                {% else %}
                <div class="no-data-message">
                    <p>No delisted products data available.</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- New Products -->
        <div class="product-section">
            <h4>New Products</h4>
            <div class="chart-wrapper">
                {% if new_chart %}
                <div id="new-chart" class="plotly-chart"></div>
                {% else %}
                <div class="no-data-message">
                    <p>No new products data available.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Country-level details expander -->
    <div class="expander-section">
        <button class="expander-toggle" onclick="toggleExpander('country-delisted-details')">
            <span class="expander-icon">▼</span>
            View Country-Level Details
        </button>
        
        <div id="country-delisted-details" class="expander-content" style="display: none;">
            {% for country in ["CZ", "HU", "SK"] %}
            <div class="country-section">
                <h4>{{ country }}</h4>
                <div hx-get="/api/delisted-new-products?country={{ country }}&level={{ level }}"
                     hx-trigger="revealed"
                     hx-include="[name='own_brand_filter'], #period-select">
                    <div class="loading-placeholder">
                        <div class="spinner-small"></div>
                        <span>Loading {{ country }} data...</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
    // Render the Plotly charts
    {% if delisted_chart %}
    const delistedData = {{ delisted_chart|safe }};
    Plotly.newPlot('delisted-chart', delistedData.data, delistedData.layout, {
        responsive: true,
        displayModeBar: false
    });
    {% endif %}
    
    {% if new_chart %}
    const newData = {{ new_chart|safe }};
    Plotly.newPlot('new-chart', newData.data, newData.layout, {
        responsive: true,
        displayModeBar: false
    });
    {% endif %}
    
    function toggleExpander(id) {
        const content = document.getElementById(id);
        const toggle = content.previousElementSibling;
        const icon = toggle.querySelector('.expander-icon');
        
        if (content.style.display === 'none') {
            content.style.display = 'block';
            icon.textContent = '▲';
            htmx.trigger(content, 'revealed');
        } else {
            content.style.display = 'none';
            icon.textContent = '▼';
        }
    }
</script>
