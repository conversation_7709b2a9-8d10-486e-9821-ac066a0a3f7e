"""
FastAPI + HTMX Replenishment Dashboard
Modern replacement for the Streamlit dashboard
"""

from fastapi import FastAP<PERSON>, Request, Depends, Query, Form
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from typing import Optional, List
import uvicorn

from app.routers import dashboard, api
from app.core.config import settings
from app.core.data_loader import DataLoader

# Initialize FastAPI app
app = FastAPI(
    title="Replenishment Types Dashboard",
    description="Modern dashboard for replenishment analysis with FastAPI and HTMX",
    version="1.0.0"
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Setup templates
templates = Jinja2Templates(directory="templates")

# Include routers
app.include_router(dashboard.router, prefix="", tags=["dashboard"])
app.include_router(api.router, prefix="/api", tags=["api"])

# Initialize data loader
data_loader = DataLoader()

@app.on_event("startup")
async def startup_event():
    """Load data on startup"""
    await data_loader.load_all_data()

@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Root endpoint - redirect to dashboard"""
    return templates.TemplateResponse(
        "dashboard.html", 
        {
            "request": request,
            "title": "Replenishment Types Dashboard",
            "current_period": "25_P6"
        }
    )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )
