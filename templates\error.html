{% extends "base.html" %}

{% block content %}
<div class="error-container">
    <div class="styled-container">
        <div class="error-content">
            <h2>⚠️ Error</h2>
            <p class="error-message">{{ error }}</p>
            <div class="error-actions">
                <a href="/" class="btn btn-primary">Return to Dashboard</a>
                <button onclick="history.back()" class="btn btn-secondary">Go Back</button>
            </div>
        </div>
    </div>
</div>

<style>
.error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
}

.error-content {
    text-align: center;
    max-width: 500px;
}

.error-content h2 {
    color: var(--error);
    margin-bottom: var(--spacing-lg);
}

.error-message {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    font-size: var(--font-size-lg);
}

.error-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}
</style>
{% endblock %}
