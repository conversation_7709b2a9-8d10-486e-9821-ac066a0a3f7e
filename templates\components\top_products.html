<div class="top-products-container">
    <div class="products-header">
        <div class="products-info">
            <h4>Top {{ repl_type.upper() }} Products - {{ country }}</h4>
            <p class="products-count">Showing {{ total_count }} products for {{ period.upper() }}</p>
        </div>
        
        <div class="export-controls">
            <a href="/api/export-excel?period={{ period }}&repl_type={{ repl_type }}&country={{ country }}&limit={{ total_count }}"
               class="btn btn-export"
               download>
                📊 Download Excel
            </a>
        </div>
    </div>
    
    {% if products_data %}
    <div class="products-table-wrapper">
        <table class="products-table">
            <thead>
                <tr>
                    {% for col in display_columns %}
                    <th>
                        {% if col == 'tpnb' %}TPNB
                        {% elif col == 'product_name' %}Product Name
                        {% elif col == 'supplier_name' %}Supplier
                        {% elif col == 'division' %}Division
                        {% elif col == 'department' %}Department
                        {% elif col == 'srp' %}SRP
                        {% elif col == 'nsrp' %}NSRP
                        {% elif col == 'mu' %}MU
                        {% elif col == 'full_pallet' %}Full Pallet
                        {% elif col == 'split_pallet' %}Split Pallet
                        {% else %}{{ col.title() }}
                        {% endif %}
                    </th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody>
                {% for product in products_data %}
                <tr>
                    {% for col in display_columns %}
                    <td class="{% if col in ['srp', 'nsrp', 'mu', 'full_pallet', 'split_pallet'] %}numeric{% endif %}">
                        {% if col == 'product_name' and product[col]|length > 40 %}
                            <span title="{{ product[col] }}">{{ product[col][:40] }}...</span>
                        {% elif col == 'supplier_name' and product[col] and product[col]|length > 30 %}
                            <span title="{{ product[col] }}">{{ product[col][:30] }}...</span>
                        {% else %}
                            {{ product[col] or '-' }}
                        {% endif %}
                    </td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Pagination controls if needed -->
    {% if total_count > 100 %}
    <div class="pagination-controls">
        <button class="btn btn-secondary" 
                hx-get="/api/top-products"
                hx-target="#section-topproducts"
                hx-include="[name='own_brand_filter'], #period-select, #repl-type-select, #country-select"
                hx-vals='{"limit": "50"}'>
            Show Top 50
        </button>
        <button class="btn btn-secondary" 
                hx-get="/api/top-products"
                hx-target="#section-topproducts"
                hx-include="[name='own_brand_filter'], #period-select, #repl-type-select, #country-select"
                hx-vals='{"limit": "100"}'>
            Show Top 100
        </button>
        <button class="btn btn-secondary" 
                hx-get="/api/top-products"
                hx-target="#section-topproducts"
                hx-include="[name='own_brand_filter'], #period-select, #repl-type-select, #country-select"
                hx-vals='{"limit": "0"}'>
            Show All
        </button>
    </div>
    {% endif %}
    
    {% else %}
    <div class="no-data-message">
        <p>No products found with the current filter settings.</p>
        <p>Try adjusting your filters or selecting a different replenishment type.</p>
    </div>
    {% endif %}
</div>
