"""
Application configuration settings
"""

from pydantic_settings import BaseSettings
from typing import List

class Settings(BaseSettings):
    """Application settings"""
    
    # Server settings
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    DEBUG: bool = True
    
    # Data settings
    DATA_DIR: str = "data"
    
    # Dashboard settings
    PERIODS: List[str] = ["25_P1", "25_P2", "25_P3", "25_P4", "25_P5", "25_P6"]
    COUNTRIES: List[str] = ["CZ", "HU", "SK"]
    REPLENISHMENT_TYPES: List[str] = ["srp", "nsrp", "mu", "full_pallet", "split_pallet"]
    
    # UI settings
    DEFAULT_PERIOD: str = "25_P6"
    ROWS_PER_PAGE: int = 50
    
    class Config:
        env_file = ".env"

settings = Settings()
