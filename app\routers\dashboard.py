"""
Dashboard routes for serving HTML pages
"""

from fastapi import APIRouter, Request, Depends, Query, Form
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from typing import Optional, List

from app.core.data_loader import data_loader
from app.core.config import settings

router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/dashboard", response_class=HTMLResponse)
@router.get("/dashboard/{period}", response_class=HTMLResponse)
async def dashboard_page(
    request: Request,
    period: str = "25p6"
):
    """Main dashboard page"""
    available_periods = data_loader.get_available_periods()
    
    return templates.TemplateResponse(
        "dashboard.html",
        {
            "request": request,
            "title": "Replenishment Types Dashboard",
            "current_period": period,
            "available_periods": available_periods,
            "periods": settings.PERIODS,
            "countries": settings.COUNTRIES
        }
    )

@router.get("/period/{period}", response_class=HTMLResponse)
async def period_page(
    request: Request,
    period: str,
    own_brand: bool = Query(False, alias="own_brand_filter")
):
    """Period-specific page with HTMX content"""
    
    # Get period data
    df = data_loader.get_dataframe(period)
    if df is None:
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error": f"No data available for period {period}"
            }
        )
    
    # Apply own brand filter if needed
    if own_brand:
        df = df[df['own_brand'] == 'Y']
    
    # Get basic stats
    total_products = len(df)
    divisions = sorted(df['division'].unique())
    countries = sorted(df['country'].unique())
    
    return templates.TemplateResponse(
        "period_content.html",
        {
            "request": request,
            "period": period,
            "own_brand_filter": own_brand,
            "total_products": total_products,
            "divisions": divisions,
            "countries": countries,
            "df_sample": df.head(10).to_dict('records') if not df.empty else []
        }
    )
