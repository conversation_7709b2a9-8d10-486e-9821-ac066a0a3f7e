{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <!-- Welcome Section -->
    <section class="welcome-section">
        <div class="styled-container">
            <h2>Welcome to Replenishment Types Dashboard</h2>
            <p>Analyze replenishment data across periods with interactive visualizations and filtering.</p>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>Current Period</h3>
                    <p class="stat-value">{{ current_period.upper() }}</p>
                </div>
                <div class="stat-card">
                    <h3>Available Periods</h3>
                    <p class="stat-value">{{ available_periods|length }}</p>
                </div>
                <div class="stat-card">
                    <h3>Countries</h3>
                    <p class="stat-value">{{ countries|length }}</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Division Comparison Section -->
    <section id="division" class="dashboard-section">
        <div class="styled-container">
            <h2>Division TPN Numbers</h2>
            <div id="section-division" 
                 hx-get="/api/division-comparison?period1=25p5&period2={{ current_period }}"
                 hx-trigger="load"
                 hx-include="[name='own_brand_filter']">
                <div class="loading-placeholder">
                    <div class="spinner"></div>
                    <p>Loading division comparison...</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Replenishment Types Section -->
    <section id="replenishment" class="dashboard-section">
        <div class="styled-container">
            <h2>Replenishment Types Changes %</h2>
            
            <!-- Level Selector -->
            <div class="section-controls">
                <div class="tab-selector">
                    <button class="tab-btn active" 
                            hx-get="/api/replenishment-stats?level=division"
                            hx-target="#section-replenishment"
                            hx-include="[name='own_brand_filter'], #period-select">
                        Division Level
                    </button>
                    <button class="tab-btn" 
                            hx-get="/api/replenishment-stats?level=department"
                            hx-target="#section-replenishment"
                            hx-include="[name='own_brand_filter'], #period-select">
                        Department Level
                    </button>
                </div>
            </div>
            
            <div id="section-replenishment" 
                 hx-get="/api/replenishment-stats?period1=25p5&period2={{ current_period }}&level=division"
                 hx-trigger="load"
                 hx-include="[name='own_brand_filter']">
                <div class="loading-placeholder">
                    <div class="spinner"></div>
                    <p>Loading replenishment statistics...</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Delisted/New Products Section -->
    <section id="delisted" class="dashboard-section">
        <div class="styled-container">
            <h2>Delisted & New Products Analysis</h2>
            
            <!-- Level Selector -->
            <div class="section-controls">
                <div class="tab-selector">
                    <button class="tab-btn active" 
                            hx-get="/api/delisted-new-products?level=division"
                            hx-target="#section-delisted"
                            hx-include="[name='own_brand_filter'], #period-select">
                        Division Level
                    </button>
                    <button class="tab-btn" 
                            hx-get="/api/delisted-new-products?level=department"
                            hx-target="#section-delisted"
                            hx-include="[name='own_brand_filter'], #period-select">
                        Department Level
                    </button>
                </div>
            </div>
            
            <div id="section-delisted" 
                 hx-get="/api/delisted-new-products?period1=25p5&period2={{ current_period }}&level=division"
                 hx-trigger="load"
                 hx-include="[name='own_brand_filter']">
                <div class="loading-placeholder">
                    <div class="spinner"></div>
                    <p>Loading delisted/new products analysis...</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Top Products Section -->
    <section id="topproducts" class="dashboard-section">
        <div class="styled-container">
            <h2>New Products by Replenishment Type</h2>
            
            <!-- Controls -->
            <div class="section-controls">
                <div class="control-group">
                    <label for="repl-type-select">Replenishment Type:</label>
                    <select id="repl-type-select" name="repl_type">
                        <option value="nsrp" selected>NSRP</option>
                        <option value="srp">SRP</option>
                        <option value="mu">MU</option>
                        <option value="full_pallet">Full Pallet</option>
                        <option value="split_pallet">Split Pallet</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="country-select">Country:</label>
                    <select id="country-select" name="country">
                        {% for country in countries %}
                        <option value="{{ country }}" {% if country == "CZ" %}selected{% endif %}>{{ country }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="limit-select">Rows:</label>
                    <select id="limit-select" name="limit">
                        <option value="10">10</option>
                        <option value="50" selected>50</option>
                        <option value="100">100</option>
                        <option value="0">All</option>
                    </select>
                </div>
                
                <button class="btn btn-primary" 
                        hx-get="/api/top-products"
                        hx-target="#section-topproducts"
                        hx-include="[name='own_brand_filter'], #period-select, #repl-type-select, #country-select, #limit-select">
                    Update
                </button>
            </div>
            
            <div id="section-topproducts" 
                 hx-get="/api/top-products?period={{ current_period }}&repl_type=nsrp&country=CZ&limit=50"
                 hx-trigger="load"
                 hx-include="[name='own_brand_filter']">
                <div class="loading-placeholder">
                    <div class="spinner"></div>
                    <p>Loading top products...</p>
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Handle tab switching
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('tab-btn')) {
            // Remove active class from all tabs in the same group
            const tabGroup = e.target.parentElement;
            tabGroup.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked tab
            e.target.classList.add('active');
        }
    });
    
    // Auto-refresh sections when filters change
    document.getElementById('own-brand-toggle').addEventListener('change', function() {
        // Trigger refresh of all sections
        setTimeout(() => {
            document.querySelectorAll('[hx-trigger="load"]').forEach(el => {
                htmx.trigger(el, 'load');
            });
        }, 100);
    });
</script>
{% endblock %}
