"""
Simple HTTP Server Dashboard - No FastAPI dependencies
This creates a working dashboard using only Python standard library + pandas
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
import locale
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import webbrowser
import time

# Set locale for number formatting
try:
    locale.setlocale(locale.LC_ALL, '')
except:
    pass

class DataLoader:
    def __init__(self):
        self.dataframes = {}
        self.data_dir = Path("data")
        
    def load_all_data(self):
        """Load all parquet files matching the pattern"""
        try:
            matching_files = list(self.data_dir.glob("CE_JDA_SRD_for_streamlit*"))
            
            if not matching_files:
                print("Warning: No matching files found in the data directory.")
                return {}
            
            for file_path in matching_files:
                try:
                    period = file_path.name.split('_')[-1]
                    df = pd.read_parquet(file_path)
                    df = df[df.division.notna()]
                    self.dataframes[period] = df
                    print(f"✅ Loaded data for period {period}: {len(df)} rows")
                except Exception as e:
                    print(f"❌ Warning: Could not read file {file_path.name}: {str(e)}")
                    continue
                    
            return self.dataframes
        except Exception as e:
            print(f"❌ Error loading data: {str(e)}")
            return {}
    
    def get_dataframe(self, period):
        return self.dataframes.get(period)
    
    def get_available_periods(self):
        return list(self.dataframes.keys())

# Global data loader
data_loader = DataLoader()

def format_number(x):
    """Format number with space as thousands separator"""
    try:
        return f"{x:,}".replace(",", " ")
    except:
        return str(x)

def create_dashboard_html(periods, sample_data):
    """Create the main dashboard HTML"""
    return f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Replenishment Types Dashboard</title>
        <script src="https://unpkg.com/htmx.org@1.9.10"></script>
        <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
        <style>
            * {{ margin: 0; padding: 0; box-sizing: border-box; }}
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
                color: #f8fafc;
                min-height: 100vh;
            }}
            .navbar {{
                background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
                border-bottom: 1px solid #475569;
                padding: 1rem 0;
                position: sticky;
                top: 0;
                z-index: 100;
                backdrop-filter: blur(10px);
            }}
            .nav-container {{
                max-width: 1400px;
                margin: 0 auto;
                padding: 0 1.5rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 1rem;
            }}
            .nav-brand h1 {{
                font-size: 1.875rem;
                font-weight: 700;
                background: linear-gradient(135deg, #60a5fa, #3b82f6);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin: 0;
            }}
            .nav-menu {{
                display: flex;
                align-items: center;
                gap: 1.5rem;
                flex-wrap: wrap;
            }}
            .period-selector {{
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }}
            .period-selector select {{
                background: #1e293b;
                border: 1px solid #475569;
                border-radius: 0.5rem;
                color: #f8fafc;
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
                cursor: pointer;
            }}
            .toggle-switch {{
                display: flex;
                align-items: center;
                gap: 0.5rem;
                cursor: pointer;
                user-select: none;
            }}
            .toggle-slider {{
                position: relative;
                width: 44px;
                height: 24px;
                background: #334155;
                border-radius: 12px;
                transition: all 0.3s ease;
                border: 1px solid #475569;
            }}
            .toggle-slider::before {{
                content: '';
                position: absolute;
                top: 2px;
                left: 2px;
                width: 18px;
                height: 18px;
                background: #f8fafc;
                border-radius: 50%;
                transition: all 0.3s ease;
            }}
            input[type="checkbox"]:checked + .toggle-slider {{
                background: #3b82f6;
                border-color: #3b82f6;
            }}
            input[type="checkbox"]:checked + .toggle-slider::before {{
                transform: translateX(20px);
            }}
            input[type="checkbox"] {{ display: none; }}
            .main-content {{
                padding: 2rem;
                max-width: 1400px;
                margin: 0 auto;
            }}
            .dashboard-section {{
                background: linear-gradient(145deg, rgba(32, 32, 40, 0.8), rgba(10, 10, 15, 0.9));
                border-radius: 1.5rem;
                padding: 2rem;
                margin-bottom: 2rem;
                border: 1px solid rgba(255, 255, 255, 0.07);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.25);
                position: relative;
                overflow: hidden;
            }}
            .dashboard-section::before {{
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(125deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 30%, rgba(0, 0, 0, 0) 100%);
                pointer-events: none;
                border-radius: 1.5rem;
            }}
            .dashboard-section h2 {{
                font-size: 1.875rem;
                font-weight: 700;
                background: linear-gradient(135deg, #60a5fa, #3b82f6);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin-bottom: 1.5rem;
                position: relative;
                z-index: 1;
            }}
            .stats-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem;
                margin-top: 2rem;
            }}
            .stat-card {{
                background: #1e293b;
                border-radius: 0.75rem;
                padding: 1.5rem;
                border: 1px solid #475569;
                text-align: center;
                transition: all 0.3s ease;
                position: relative;
                z-index: 1;
            }}
            .stat-card:hover {{
                transform: translateY(-2px);
                box-shadow: 0 10px 15px rgba(0, 0, 0, 0.3);
                border-color: #3b82f6;
            }}
            .stat-card h3 {{
                font-size: 0.875rem;
                color: #94a3b8;
                margin-bottom: 0.5rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }}
            .stat-value {{
                font-size: 1.5rem;
                font-weight: 700;
                color: #3b82f6;
            }}
            .btn {{
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem 1.5rem;
                border-radius: 0.5rem;
                font-weight: 500;
                font-size: 0.875rem;
                text-decoration: none;
                border: none;
                cursor: pointer;
                transition: all 0.2s ease;
                white-space: nowrap;
            }}
            .btn-primary {{
                background: #3b82f6;
                color: white;
            }}
            .btn-primary:hover {{
                background: #2563eb;
                transform: translateY(-1px);
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            }}
            .section-content {{
                min-height: 200px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #94a3b8;
                position: relative;
                z-index: 1;
            }}
            .data-table {{
                width: 100%;
                border-collapse: collapse;
                font-size: 0.875rem;
                background: #1e293b;
                border-radius: 0.75rem;
                overflow: hidden;
                margin-top: 1rem;
            }}
            .data-table th {{
                background: #334155;
                color: #f8fafc;
                padding: 0.75rem 1rem;
                text-align: left;
                font-weight: 600;
                border-bottom: 1px solid #475569;
            }}
            .data-table td {{
                padding: 0.75rem 1rem;
                border-bottom: 1px solid #475569;
                color: #f8fafc;
            }}
            .data-table tr:hover {{
                background: #334155;
            }}
            .positive {{ color: #10b981; }}
            .negative {{ color: #ef4444; }}
            .loading {{
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 1rem;
                padding: 2rem;
            }}
            .spinner {{
                width: 40px;
                height: 40px;
                border: 3px solid #334155;
                border-top: 3px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }}
            @keyframes spin {{
                0% {{ transform: rotate(0deg); }}
                100% {{ transform: rotate(360deg); }}
            }}
            @media (max-width: 768px) {{
                .nav-container {{ flex-direction: column; }}
                .stats-grid {{ grid-template-columns: 1fr; }}
                .main-content {{ padding: 1rem; }}
            }}
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <h1>🚀 Replenishment Types Dashboard</h1>
                </div>
                
                <div class="nav-menu">
                    <div class="period-selector">
                        <label for="period-select">Period:</label>
                        <select id="period-select" 
                                hx-get="/api/period-data" 
                                hx-target="#main-content"
                                hx-trigger="change"
                                hx-include="[name='own_brand_filter']">
                            {' '.join(f'<option value="{period}">{period.upper()}</option>' for period in periods)}
                        </select>
                    </div>
                    
                    <div class="toggle-switch">
                        <input type="checkbox" 
                               name="own_brand_filter" 
                               id="own-brand-toggle"
                               hx-get="/api/period-data"
                               hx-target="#main-content"
                               hx-trigger="change"
                               hx-include="#period-select">
                        <span class="toggle-slider"></span>
                        <span>OwnBrand Only</span>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div id="main-content">
                <!-- Welcome Section -->
                <section class="dashboard-section">
                    <h2>📊 Dashboard Overview</h2>
                    <p>Modern replenishment analytics platform built with Python and HTMX</p>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <h3>Periods Available</h3>
                            <div class="stat-value">{len(periods)}</div>
                        </div>
                        <div class="stat-card">
                            <h3>Total Records</h3>
                            <div class="stat-value">{sample_data.get('total_rows', 0):,}</div>
                        </div>
                        <div class="stat-card">
                            <h3>Divisions</h3>
                            <div class="stat-value">{len(sample_data.get('divisions', []))}</div>
                        </div>
                        <div class="stat-card">
                            <h3>Countries</h3>
                            <div class="stat-value">{len(sample_data.get('countries', []))}</div>
                        </div>
                    </div>
                </section>

                <!-- Division Comparison Section -->
                <section class="dashboard-section">
                    <h2>📈 Division TPN Numbers</h2>
                    <div id="division-section" 
                         hx-get="/api/division-comparison"
                         hx-trigger="load"
                         hx-include="[name='own_brand_filter'], #period-select">
                        <div class="loading">
                            <div class="spinner"></div>
                            <p>Loading division comparison...</p>
                        </div>
                    </div>
                </section>

                <!-- Status Section -->
                <section class="dashboard-section">
                    <h2>✅ System Status</h2>
                    <p>Dashboard is running successfully with Python HTTP server</p>
                    <div style="margin-top: 1rem;">
                        <a href="/health" class="btn btn-primary">🔍 Health Check</a>
                        <a href="/api/data" class="btn btn-primary">📊 Raw Data</a>
                    </div>
                </section>
            </div>
        </main>

        <script>
            // Auto-trigger division comparison load
            document.addEventListener('DOMContentLoaded', function() {{
                htmx.trigger('#division-section', 'load');
            }});
        </script>
    </body>
    </html>
    """

class DashboardHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        query_params = parse_qs(parsed_path.query)
        
        try:
            if path == '/':
                self.serve_dashboard()
            elif path == '/health':
                self.serve_health()
            elif path == '/api/data':
                self.serve_data()
            elif path == '/api/division-comparison':
                self.serve_division_comparison(query_params)
            else:
                self.send_error(404, "Not Found")
        except Exception as e:
            print(f"Error handling request: {e}")
            self.send_error(500, f"Internal Server Error: {e}")
    
    def serve_dashboard(self):
        periods = data_loader.get_available_periods()
        sample_data = {}
        
        if periods:
            df = data_loader.get_dataframe(periods[0])
            if df is not None:
                sample_data = {
                    'total_rows': len(df),
                    'divisions': sorted(df['division'].unique()) if 'division' in df.columns else [],
                    'countries': sorted(df['country'].unique()) if 'country' in df.columns else []
                }
        
        html_content = create_dashboard_html(periods, sample_data)
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html_content.encode())
    
    def serve_health(self):
        periods = data_loader.get_available_periods()
        health_data = {
            "status": "healthy",
            "periods_loaded": len(periods),
            "available_periods": periods,
            "message": "Dashboard is running successfully!"
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(health_data, indent=2).encode())
    
    def serve_data(self):
        periods = data_loader.get_available_periods()
        data_summary = {}
        
        for period in periods:
            df = data_loader.get_dataframe(period)
            if df is not None:
                data_summary[period] = {
                    "rows": len(df),
                    "divisions": sorted(df['division'].unique().tolist()) if 'division' in df.columns else [],
                    "countries": sorted(df['country'].unique().tolist()) if 'country' in df.columns else []
                }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(data_summary, indent=2).encode())
    
    def serve_division_comparison(self, query_params):
        period1 = query_params.get('period1', ['25p5'])[0]
        period2 = query_params.get('period2', ['25p6'])[0]
        own_brand = query_params.get('own_brand_filter', ['false'])[0].lower() == 'true'
        
        df1 = data_loader.get_dataframe(period1)
        df2 = data_loader.get_dataframe(period2)
        
        if df1 is None or df2 is None:
            html_content = "<p>Period data not found</p>"
        else:
            # Apply filters
            if own_brand:
                df1 = df1[df1['own_brand'] == 'Y']
                df2 = df2[df2['own_brand'] == 'Y']
            
            # Calculate counts for each division
            df1_counts = df1.groupby('division')['tpnb'].nunique().reset_index()
            df2_counts = df2.groupby('division')['tpnb'].nunique().reset_index()
            
            # Merge the counts to calculate differences
            merged_counts = pd.merge(df1_counts, df2_counts, on='division', suffixes=('_1', '_2'))
            merged_counts['difference'] = merged_counts['tpnb_2'] - merged_counts['tpnb_1']
            merged_counts['pct_change'] = ((merged_counts['tpnb_2'] - merged_counts['tpnb_1']) / merged_counts['tpnb_1'] * 100).round(1)
            
            # Filter and sort
            merged_counts = merged_counts[merged_counts['difference'] != 0].copy()
            merged_counts = merged_counts.sort_values('tpnb_2', ascending=False)
            
            # Create HTML table
            html_content = f"""
            <div style="background: #1e293b; border-radius: 0.75rem; padding: 1.5rem; border: 1px solid #475569;">
                <h3 style="color: #3b82f6; margin-bottom: 1rem;">Division Comparison: {period1.upper()} vs {period2.upper()}</h3>
                <div style="overflow-x: auto;">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Division</th>
                                <th style="text-align: right;">{period1.upper()}</th>
                                <th style="text-align: right;">{period2.upper()}</th>
                                <th style="text-align: right;">Difference</th>
                                <th style="text-align: right;">Change %</th>
                            </tr>
                        </thead>
                        <tbody>
            """
            
            for _, row in merged_counts.iterrows():
                color = "positive" if row['difference'] > 0 else "negative"
                arrow = "↑" if row['difference'] > 0 else "↓"
                html_content += f"""
                            <tr>
                                <td>{row['division']}</td>
                                <td style="text-align: right;">{format_number(row['tpnb_1'])}</td>
                                <td style="text-align: right;">{format_number(row['tpnb_2'])}</td>
                                <td style="text-align: right;" class="{color}">
                                    {arrow} {format_number(abs(row['difference']))}
                                </td>
                                <td style="text-align: right;" class="{color}">
                                    {row['pct_change']:+.1f}%
                                </td>
                            </tr>
                """
            
            html_content += """
                        </tbody>
                    </table>
                </div>
            </div>
            """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html_content.encode())

def run_server():
    """Run the HTTP server"""
    server_address = ('127.0.0.1', 8000)
    httpd = HTTPServer(server_address, DashboardHandler)
    
    print("🚀 Starting Simple HTTP Dashboard Server...")
    print("📱 Dashboard will be available at: http://127.0.0.1:8000")
    print("🔍 Health check at: http://127.0.0.1:8000/health")
    print("📊 Raw data at: http://127.0.0.1:8000/api/data")
    print("\n⏹️  Press Ctrl+C to stop the server\n")
    
    # Load data
    data_loader.load_all_data()
    periods = data_loader.get_available_periods()
    if periods:
        print(f"✅ Dashboard ready with periods: {periods}")
    else:
        print("⚠️ No data loaded")
    
    # Open browser after a short delay
    def open_browser():
        time.sleep(2)
        webbrowser.open('http://127.0.0.1:8000')
    
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Dashboard stopped!")
        httpd.shutdown()

if __name__ == "__main__":
    run_server()
