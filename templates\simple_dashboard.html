<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #0f172a;
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        .status-card {
            background: linear-gradient(145deg, rgba(32, 32, 40, 0.8), rgba(10, 10, 15, 0.9));
            border-radius: 1rem;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.07);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.25);
            margin-bottom: 2rem;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .stat-item {
            background: #1e293b;
            border-radius: 0.5rem;
            padding: 1.5rem;
            border: 1px solid #475569;
            text-align: center;
        }
        
        .stat-item h3 {
            color: #94a3b8;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
        }
        
        .periods-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .period-tag {
            background: #3b82f6;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .success {
            color: #10b981;
        }
        
        .error {
            color: #ef4444;
        }
        
        .list-items {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .list-item {
            background: #334155;
            color: #f8fafc;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
        }
        
        .next-steps {
            background: #1e293b;
            border-radius: 1rem;
            padding: 2rem;
            border: 1px solid #475569;
            margin-top: 2rem;
        }
        
        .next-steps h2 {
            color: #3b82f6;
            margin-bottom: 1rem;
        }
        
        .next-steps ul {
            list-style: none;
            padding: 0;
        }
        
        .next-steps li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #334155;
        }
        
        .next-steps li:last-child {
            border-bottom: none;
        }
        
        .next-steps li::before {
            content: "✅ ";
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ title }}</h1>
            <p>FastAPI + HTMX Modern Dashboard</p>
        </div>
        
        <div class="status-card">
            <h2>🚀 Application Status</h2>
            
            {% if periods %}
            <p class="success">✅ Application is running successfully!</p>
            <p>Data has been loaded and the dashboard is ready to use.</p>
            {% else %}
            <p class="error">❌ No data loaded</p>
            <p>Please check that parquet files are available in the data directory.</p>
            {% endif %}
            
            <div class="status-grid">
                <div class="stat-item">
                    <h3>Periods Loaded</h3>
                    <div class="stat-value">{{ periods|length }}</div>
                    {% if periods %}
                    <div class="periods-list">
                        {% for period in periods %}
                        <span class="period-tag">{{ period.upper() }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                {% if sample_data %}
                <div class="stat-item">
                    <h3>Total Records</h3>
                    <div class="stat-value">{{ "{:,}".format(sample_data.total_rows) }}</div>
                </div>
                
                <div class="stat-item">
                    <h3>Divisions</h3>
                    <div class="stat-value">{{ sample_data.divisions|length }}</div>
                    <div class="list-items">
                        {% for division in sample_data.divisions[:5] %}
                        <span class="list-item">{{ division }}</span>
                        {% endfor %}
                        {% if sample_data.divisions|length > 5 %}
                        <span class="list-item">+{{ sample_data.divisions|length - 5 }} more</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="stat-item">
                    <h3>Countries</h3>
                    <div class="stat-value">{{ sample_data.countries|length }}</div>
                    <div class="list-items">
                        {% for country in sample_data.countries %}
                        <span class="list-item">{{ country }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="next-steps">
            <h2>🎯 Implementation Status</h2>
            <ul>
                <li>FastAPI backend application structure</li>
                <li>Data loading from parquet files</li>
                <li>Modern dark theme styling</li>
                <li>Template system with Jinja2</li>
                <li>Static file serving</li>
                <li>Health check endpoint</li>
                <li>Error handling and logging</li>
            </ul>
            
            <h3 style="color: #60a5fa; margin-top: 2rem;">🔗 Available Endpoints</h3>
            <ul style="margin-top: 1rem;">
                <li><strong>/</strong> - This dashboard page</li>
                <li><strong>/health</strong> - Health check and status</li>
                <li><strong>/docs</strong> - FastAPI automatic documentation</li>
            </ul>
            
            <p style="margin-top: 2rem; color: #94a3b8;">
                The application is successfully running! The full dashboard with HTMX interactions 
                can be implemented by extending this foundation.
            </p>
        </div>
    </div>
</body>
</html>
