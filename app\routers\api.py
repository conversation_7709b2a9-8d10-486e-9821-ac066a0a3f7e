"""
API routes for data endpoints
"""

from fastapi import APIRouter, Request, Query, HTTPException, Form
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, JSONResponse, StreamingResponse
from typing import Optional, List
import pandas as pd
import plotly.graph_objects as go
import plotly.utils
import json
from io import BytesIO
import locale

from app.core.data_loader import data_loader
from app.core.config import settings
from app.models.dashboard import FilterRequest

router = APIRouter()
templates = Jinja2Templates(directory="templates")

# Helper functions
def format_number(x):
    """Format number with space as thousands separator"""
    try:
        return locale.format_string("%d", x, grouping=True).replace(",", " ")
    except:
        return str(x)

def format_k(number):
    """Format numbers in K format"""
    if number >= 1000:
        return f"{number/1000:.1f}K"
    return str(number)

@router.get("/division-comparison", response_class=HTMLResponse)
async def division_comparison(
    request: Request,
    period1: str = Query("25p5"),
    period2: str = Query("25p6"),
    own_brand: bool = Query(False),
    country: Optional[str] = Query(None)
):
    """Get division comparison data and chart"""
    
    df1 = data_loader.get_dataframe(period1)
    df2 = data_loader.get_dataframe(period2)
    
    if df1 is None or df2 is None:
        raise HTTPException(status_code=404, detail="Period data not found")
    
    # Apply filters
    if own_brand:
        df1 = df1[df1['own_brand'] == 'Y']
        df2 = df2[df2['own_brand'] == 'Y']
    
    if country:
        df1 = df1[df1['country'] == country]
        df2 = df2[df2['country'] == country]
    
    # Calculate counts for each division
    df1_counts = df1.groupby('division')['tpnb'].nunique().reset_index()
    df2_counts = df2.groupby('division')['tpnb'].nunique().reset_index()
    
    # Merge the counts to calculate differences
    merged_counts = pd.merge(df1_counts, df2_counts, on='division', suffixes=('_1', '_2'))
    merged_counts['difference'] = merged_counts['tpnb_2'] - merged_counts['tpnb_1']
    merged_counts['pct_change'] = ((merged_counts['tpnb_2'] - merged_counts['tpnb_1']) / merged_counts['tpnb_1'] * 100).round(1)
    
    # Filter out divisions with no difference and sort by Period 2 TPNB count
    merged_counts = merged_counts[merged_counts['difference'] != 0].copy()
    merged_counts = merged_counts.sort_values('tpnb_2', ascending=False)
    
    # Create Plotly chart
    fig = go.Figure()
    
    # Add bars for Period 1
    fig.add_trace(go.Bar(
        name=f"Period {period1[-1]}",
        x=merged_counts['division'],
        y=merged_counts['tpnb_1'],
        marker_color='rgb(55, 83, 109)',
        text=merged_counts['tpnb_1'].apply(format_k),
        textposition='outside'
    ))
    
    # Add bars for Period 2
    fig.add_trace(go.Bar(
        name=f"Period {period2[-1]}",
        x=merged_counts['division'],
        y=merged_counts['tpnb_2'],
        marker_color='rgb(26, 118, 255)',
        text=merged_counts['difference'].apply(lambda x: f"{'+' if x > 0 else ''}{format_number(x)}"),
        textposition='outside'
    ))
    
    # Update layout
    fig.update_layout(
        title='TPNB Count Comparison by Division',
        xaxis_title='Division',
        yaxis_title='Number of TPNBs',
        barmode='group',
        bargap=0.15,
        bargroupgap=0.1,
        template='plotly_dark',
        height=450,
        showlegend=True,
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        font=dict(color='white')
    )
    
    # Convert to JSON for frontend
    chart_json = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
    
    # Prepare table data
    table_data = []
    for _, row in merged_counts.iterrows():
        table_data.append({
            'division': row['division'],
            'period1': format_number(row['tpnb_1']),
            'period2': format_number(row['tpnb_2']),
            'difference': row['difference'],
            'pct_change': row['pct_change']
        })
    
    return templates.TemplateResponse(
        "components/division_comparison.html",
        {
            "request": request,
            "chart_json": chart_json,
            "table_data": table_data,
            "period1": period1,
            "period2": period2,
            "country_filter": country or "All Countries"
        }
    )

@router.get("/replenishment-stats", response_class=HTMLResponse)
async def replenishment_stats(
    request: Request,
    period1: str = Query("25p5"),
    period2: str = Query("25p6"),
    own_brand: bool = Query(False),
    level: str = Query("division"),  # "division" or "department"
    divisions: Optional[str] = Query(None),
    departments: Optional[str] = Query(None),
    country: Optional[str] = Query(None)
):
    """Get replenishment type statistics"""
    
    # Get merged data
    df_merged, df_merged_total, df_merged_division = data_loader.calculate_repl_type_stats(
        period1, period2, own_brand
    )
    
    if df_merged.empty:
        raise HTTPException(status_code=404, detail="No data available")
    
    # Select appropriate dataframe based on level
    if level == "division":
        df = df_merged_division
    else:
        df = df_merged_total
    
    # Apply filters
    if divisions:
        division_list = divisions.split(',')
        df = df[df['division'].isin(division_list)]
    
    if departments and level == "department":
        dept_list = departments.split(',')
        df = df[df['department'].isin(dept_list)]
    
    if country and level == "department":
        # For department level, filter by country from the country-level data
        country_df = df_merged[df_merged['country'] == country]
        if divisions:
            division_list = divisions.split(',')
            country_df = country_df[country_df['division'].isin(division_list)]
        if departments:
            dept_list = departments.split(',')
            country_df = country_df[country_df['department'].isin(dept_list)]
        df = country_df
    
    # Create heatmap data
    repl_types = settings.REPLENISHMENT_TYPES
    
    if level == "division":
        index_col = 'division'
        df['display_name'] = df['division']
    else:
        index_col = 'department'
        if 'department' in df.columns:
            df['display_name'] = df['division'] + ' - ' + df['department']
        else:
            df['display_name'] = df['division']
    
    # Prepare heatmap data
    heatmap_data = []
    for _, row in df.iterrows():
        for rtype in repl_types:
            heatmap_data.append({
                'x': rtype.upper().replace('_', ' '),
                'y': row['display_name'],
                'z': row[f'{rtype}_diff_pct']
            })
    
    # Create Plotly heatmap
    if heatmap_data:
        x_vals = [d['x'] for d in heatmap_data]
        y_vals = [d['y'] for d in heatmap_data]
        z_vals = [d['z'] for d in heatmap_data]
        
        # Create pivot-like structure
        unique_x = list(dict.fromkeys(x_vals))
        unique_y = list(dict.fromkeys(y_vals))
        
        z_matrix = []
        for y in unique_y:
            row = []
            for x in unique_x:
                # Find corresponding z value
                z_val = 0
                for i, (hx, hy) in enumerate(zip(x_vals, y_vals)):
                    if hx == x and hy == y:
                        z_val = z_vals[i]
                        break
                row.append(z_val)
            z_matrix.append(row)
        
        fig = go.Figure(data=go.Heatmap(
            x=unique_x,
            y=unique_y,
            z=z_matrix,
            text=[[f'{val:.1f}%' for val in row] for row in z_matrix],
            texttemplate='%{text}',
            textfont={"size": 15},
            hoverongaps=False,
            colorscale='rdylgn',
            zmid=0,
            colorbar_title="% Change"
        ))
        
        fig.update_layout(
            title=f'Replenishment Type Changes by {level.capitalize()}',
            height=max(400, len(unique_y) * 30),
            margin=dict(t=50, l=200),
            template='plotly_dark',
            paper_bgcolor='rgba(0,0,0,0)',
            plot_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white')
        )
        
        chart_json = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
    else:
        chart_json = None
    
    # Prepare table data
    table_data = []
    for _, row in df.iterrows():
        row_data = {
            'division': row['division'],
        }
        if 'department' in row:
            row_data['department'] = row['department']
        
        for rtype in repl_types:
            row_data[rtype] = row[f'{rtype}_diff_pct']
        
        table_data.append(row_data)
    
    return templates.TemplateResponse(
        "components/replenishment_stats.html",
        {
            "request": request,
            "chart_json": chart_json,
            "table_data": table_data,
            "level": level,
            "repl_types": repl_types,
            "period1": period1,
            "period2": period2
        }
    )

@router.get("/delisted-new-products", response_class=HTMLResponse)
async def delisted_new_products(
    request: Request,
    period1: str = Query("25p5"),
    period2: str = Query("25p6"),
    own_brand: bool = Query(False),
    level: str = Query("division"),  # "division" or "department"
    divisions: Optional[str] = Query(None),
    country: Optional[str] = Query(None)
):
    """Get delisted and new products analysis"""

    df1 = data_loader.get_dataframe(period1)
    df2 = data_loader.get_dataframe(period2)

    if df1 is None or df2 is None:
        raise HTTPException(status_code=404, detail="Period data not found")

    # Apply own brand filter
    if own_brand:
        df1 = df1[df1['own_brand'] == 'Y']
        df2 = df2[df2['own_brand'] == 'Y']

    # Get delisted and new products
    delisted_df, new_df = data_loader.create_delisted_new_products_dfs(df1, df2)

    # Apply country filter if specified
    if country:
        delisted_df = delisted_df[delisted_df['country'] == country]
        new_df = new_df[new_df['country'] == country]

    # Apply division filter if specified
    if divisions:
        division_list = divisions.split(',')
        delisted_df = delisted_df[delisted_df['division'].isin(division_list)]
        new_df = new_df[new_df['division'].isin(division_list)]

    repl_types = settings.REPLENISHMENT_TYPES

    # Group by level
    if level == "division":
        delisted_grouped = delisted_df.groupby('division')[repl_types].sum().reset_index()
        new_grouped = new_df.groupby('division')[repl_types].sum().reset_index()
    else:
        delisted_grouped = delisted_df.groupby(['division', 'department'])[repl_types].sum().reset_index()
        new_grouped = new_df.groupby(['division', 'department'])[repl_types].sum().reset_index()

    # Create charts for delisted and new products
    charts = {}

    for product_type, grouped_df in [("delisted", delisted_grouped), ("new", new_grouped)]:
        if not grouped_df.empty:
            # Prepare heatmap data
            if level == "division":
                grouped_df['display_name'] = grouped_df['division']
            else:
                grouped_df['display_name'] = grouped_df['division'] + ' - ' + grouped_df['department']

            # Create matrix for heatmap
            x_vals = [rtype.upper().replace('_', ' ') for rtype in repl_types]
            y_vals = grouped_df['display_name'].tolist()
            z_matrix = []

            for _, row in grouped_df.iterrows():
                z_row = [row[rtype] for rtype in repl_types]
                z_matrix.append(z_row)

            fig = go.Figure(data=go.Heatmap(
                x=x_vals,
                y=y_vals,
                z=z_matrix,
                text=[[format_number(val) for val in row] for row in z_matrix],
                texttemplate='%{text}',
                textfont={"size": 15},
                hoverongaps=False,
                colorscale='Blues',
                showscale=True,
                colorbar_title="Count"
            ))

            fig.update_layout(
                title=f'{product_type.capitalize()} Products by {level.capitalize()}',
                height=max(350, len(y_vals) * 30),
                margin=dict(t=50, l=200),
                template='plotly_dark',
                paper_bgcolor='rgba(0,0,0,0)',
                plot_bgcolor='rgba(0,0,0,0)',
                font=dict(color='white')
            )

            charts[product_type] = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
        else:
            charts[product_type] = None

    return templates.TemplateResponse(
        "components/delisted_new_products.html",
        {
            "request": request,
            "delisted_chart": charts.get("delisted"),
            "new_chart": charts.get("new"),
            "delisted_data": delisted_grouped.to_dict('records') if not delisted_grouped.empty else [],
            "new_data": new_grouped.to_dict('records') if not new_grouped.empty else [],
            "level": level,
            "repl_types": repl_types,
            "period1": period1,
            "period2": period2
        }
    )

@router.get("/top-products", response_class=HTMLResponse)
async def top_products(
    request: Request,
    period: str = Query("25p6"),
    repl_type: str = Query("nsrp"),
    country: str = Query("CZ"),
    own_brand: bool = Query(False),
    divisions: Optional[str] = Query(None),
    departments: Optional[str] = Query(None),
    limit: int = Query(50)
):
    """Get top products by replenishment type"""

    df = data_loader.get_dataframe(period)
    if df is None:
        raise HTTPException(status_code=404, detail="Period data not found")

    # Apply filters
    if own_brand:
        df = df[df['own_brand'] == 'Y']

    df = df[df['country'] == country]

    if divisions:
        division_list = divisions.split(',')
        df = df[df['division'].isin(division_list)]

    if departments:
        dept_list = departments.split(',')
        df = df[df['department'].isin(dept_list)]

    # Filter by replenishment type > 0 and sort
    df_filtered = df[df[repl_type] > 0].sort_values(by=repl_type, ascending=False)

    if limit > 0:
        df_filtered = df_filtered.head(limit)

    # Prepare display data
    display_columns = ['tpnb', 'division', 'department', 'product_name', 'srp', 'nsrp', 'mu', 'full_pallet', 'split_pallet', 'supplier_name']
    display_columns = [col for col in display_columns if col in df_filtered.columns]

    products_data = []
    for _, row in df_filtered.iterrows():
        product = {}
        for col in display_columns:
            if col in ['srp', 'nsrp', 'mu', 'full_pallet', 'split_pallet']:
                product[col] = format_number(row[col]) if pd.notna(row[col]) else '-'
            else:
                product[col] = row[col]
        products_data.append(product)

    return templates.TemplateResponse(
        "components/top_products.html",
        {
            "request": request,
            "products_data": products_data,
            "repl_type": repl_type,
            "country": country,
            "period": period,
            "total_count": len(df_filtered),
            "display_columns": display_columns
        }
    )

@router.get("/export-excel")
async def export_excel(
    period: str = Query("25p6"),
    repl_type: str = Query("nsrp"),
    country: str = Query("CZ"),
    own_brand: bool = Query(False),
    divisions: Optional[str] = Query(None),
    departments: Optional[str] = Query(None),
    limit: int = Query(0)
):
    """Export data to Excel"""

    df = data_loader.get_dataframe(period)
    if df is None:
        raise HTTPException(status_code=404, detail="Period data not found")

    # Apply same filters as top_products
    if own_brand:
        df = df[df['own_brand'] == 'Y']

    df = df[df['country'] == country]

    if divisions:
        division_list = divisions.split(',')
        df = df[df['division'].isin(division_list)]

    if departments:
        dept_list = departments.split(',')
        df = df[df['department'].isin(dept_list)]

    # Filter by replenishment type > 0 and sort
    df_filtered = df[df[repl_type] > 0].sort_values(by=repl_type, ascending=False)

    if limit > 0:
        df_filtered = df_filtered.head(limit)

    # Create Excel file
    output = BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df_filtered.to_excel(writer, index=False, sheet_name=f"{country}_{repl_type}")

    output.seek(0)

    filename = f"{country}_{repl_type}_top_{limit if limit > 0 else 'all'}_{period}.xlsx"

    return StreamingResponse(
        BytesIO(output.read()),
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )
