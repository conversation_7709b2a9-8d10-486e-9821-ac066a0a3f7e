/* Modern Dark Theme for Replenishment Dashboard */

:root {
    /* Color Palette */
    --primary: #3b82f6;
    --primary-dark: #2563eb;
    --primary-light: #60a5fa;
    --background: #0f172a;
    --surface: #1e293b;
    --surface-dark: #334155;
    --text: #f8fafc;
    --text-secondary: #94a3b8;
    --border: #475569;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --excel-green: #217346;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Typography */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    background-color: var(--background);
    color: var(--text);
    line-height: 1.6;
    font-size: var(--font-size-base);
    overflow-x: hidden;
}

/* Layout */
.navbar {
    background: linear-gradient(135deg, var(--surface) 0%, var(--surface-dark) 100%);
    border-bottom: 1px solid var(--border);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.nav-brand h1 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-light), var(--primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.period-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.period-selector label {
    font-weight: 500;
    color: var(--text-secondary);
}

.period-selector select {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    color: var(--text);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    transition: all 0.2s ease;
}

.period-selector select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Toggle Switch */
.toggle-switch {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    user-select: none;
}

.toggle-switch input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 44px;
    height: 24px;
    background: var(--surface-dark);
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 1px solid var(--border);
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 18px;
    height: 18px;
    background: var(--text);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.toggle-switch input[type="checkbox"]:checked + .toggle-slider {
    background: var(--primary);
    border-color: var(--primary);
}

.toggle-switch input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(20px);
}

.toggle-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

/* Sidebar */
.sidebar {
    position: fixed;
    left: 0;
    top: 80px;
    width: 280px;
    height: calc(100vh - 80px);
    background: var(--surface);
    border-right: 1px solid var(--border);
    padding: var(--spacing-lg);
    overflow-y: auto;
    z-index: 50;
}

.sidebar-content h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--text);
}

.nav-links {
    list-style: none;
}

.nav-links li {
    margin-bottom: var(--spacing-sm);
}

.nav-link {
    display: block;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    font-weight: 500;
    border: 1px solid transparent;
}

.nav-link:hover {
    background: var(--surface-dark);
    color: var(--text);
    transform: translateX(4px);
    border-color: var(--border);
}

.nav-link.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary-light);
}

/* Main Content */
.main-content {
    margin-left: 280px;
    padding: var(--spacing-xl);
    min-height: calc(100vh - 80px);
}

.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
}

/* Sections */
.dashboard-section {
    margin-bottom: var(--spacing-2xl);
}

.welcome-section {
    margin-bottom: var(--spacing-2xl);
}

.styled-container {
    background: linear-gradient(145deg, rgba(32, 32, 40, 0.8), rgba(10, 10, 15, 0.9));
    border-radius: var(--radius-2xl);
    padding: var(--spacing-xl);
    border: 1px solid rgba(255, 255, 255, 0.07);
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

.styled-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(125deg, 
                              rgba(255, 255, 255, 0.05) 0%, 
                              rgba(255, 255, 255, 0.02) 30%, 
                              rgba(0, 0, 0, 0) 100%);
    pointer-events: none;
    border-radius: var(--radius-2xl);
}

.styled-container h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-light), var(--primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.stat-card {
    background: var(--surface);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border);
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary);
}

.stat-card h3 {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary);
}

/* Controls */
.section-controls {
    margin-bottom: var(--spacing-xl);
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    align-items: center;
}

.tab-selector {
    display: flex;
    background: var(--surface);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
    border: 1px solid var(--border);
}

.tab-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.tab-btn:hover {
    background: var(--surface-dark);
    color: var(--text);
}

.tab-btn.active {
    background: var(--primary);
    color: white;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.control-group label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

.control-group select {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    color: var(--text);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    min-width: 120px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 500;
    font-size: var(--font-size-sm);
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--surface);
    color: var(--text);
    border: 1px solid var(--border);
}

.btn-secondary:hover {
    background: var(--surface-dark);
    border-color: var(--primary);
}

.btn-export {
    background: var(--excel-green);
    color: white;
}

.btn-export:hover {
    background: #1a5a37;
    transform: translateY(-1px);
}

/* Loading States */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    z-index: 1000;
    box-shadow: var(--shadow-xl);
}

.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--surface-dark);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-small {
    width: 20px;
    height: 20px;
    border: 2px solid var(--surface-dark);
    border-top: 2px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .nav-container {
        padding: 0 var(--spacing-md);
    }
    
    .nav-menu {
        width: 100%;
        justify-content: space-between;
    }
}

/* Charts and Tables */
.chart-container {
    background: var(--surface);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 1px solid var(--border);
    margin-bottom: var(--spacing-lg);
}

.chart-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-xl);
    align-items: start;
}

.chart-wrapper {
    background: var(--background);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border);
}

.plotly-chart {
    width: 100%;
    height: 450px;
}

.table-section {
    background: var(--background);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border);
}

.table-section h3,
.table-section h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text);
    font-size: var(--font-size-lg);
}

.data-table-wrapper {
    overflow-x: auto;
    border-radius: var(--radius-md);
    border: 1px solid var(--border);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
    background: var(--surface);
}

.data-table th {
    background: var(--surface-dark);
    color: var(--text);
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: left;
    font-weight: 600;
    border-bottom: 1px solid var(--border);
    white-space: nowrap;
}

.data-table td {
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border);
    color: var(--text);
    white-space: nowrap;
}

.data-table tr:hover {
    background: var(--surface-dark);
}

.data-table .numeric {
    text-align: right;
    font-family: 'Monaco', 'Menlo', monospace;
}

.data-table .positive {
    color: var(--success);
}

.data-table .negative {
    color: var(--error);
}

.data-table .neutral {
    color: var(--text-secondary);
}

/* Products Table */
.products-table-wrapper {
    overflow-x: auto;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
    margin-top: var(--spacing-lg);
}

.products-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
    background: var(--surface);
}

.products-table th {
    background: var(--surface-dark);
    color: var(--text);
    padding: var(--spacing-md);
    text-align: left;
    font-weight: 600;
    border-bottom: 1px solid var(--border);
    position: sticky;
    top: 0;
    z-index: 10;
}

.products-table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border);
    color: var(--text);
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.products-table tr:hover {
    background: var(--surface-dark);
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.products-info h4 {
    font-size: var(--font-size-lg);
    color: var(--text);
    margin-bottom: var(--spacing-xs);
}

.products-count {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Delisted/New Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
}

.product-section {
    background: var(--surface);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border);
}

.product-section h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text);
    text-align: center;
}

/* Expander Sections */
.expander-section {
    margin-top: var(--spacing-xl);
    border-top: 1px solid var(--border);
    padding-top: var(--spacing-lg);
}

.expander-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text);
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    width: 100%;
    text-align: left;
}

.expander-toggle:hover {
    background: var(--surface-dark);
    border-color: var(--primary);
}

.expander-icon {
    transition: transform 0.2s ease;
    color: var(--primary);
}

.expander-content {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--background);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
}

.country-section {
    margin-bottom: var(--spacing-xl);
}

.country-section h4 {
    color: var(--primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border);
}

/* No Data Messages */
.no-data-message {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
    background: var(--surface);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
}

.no-data-message p {
    margin-bottom: var(--spacing-sm);
}

/* Pagination */
.pagination-controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
    flex-wrap: wrap;
}

/* Filter Sections */
.filter-section {
    background: var(--surface);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border);
    margin-bottom: var(--spacing-lg);
}

.filter-row {
    display: flex;
    gap: var(--spacing-lg);
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    min-width: 200px;
}

.filter-group select[multiple] {
    min-height: 100px;
    background: var(--background);
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .section-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .tab-selector {
        justify-content: center;
    }

    .nav-brand h1 {
        font-size: var(--font-size-xl);
    }

    .chart-section {
        grid-template-columns: 1fr;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .products-header {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-row {
        flex-direction: column;
    }

    .pagination-controls {
        flex-direction: column;
    }
}
