<div class="replenishment-stats-container">
    {% if level == "department" %}
    <!-- Department Level Filters -->
    <div class="filter-section">
        <div class="filter-row">
            <div class="filter-group">
                <label for="divisions-filter">Select Divisions:</label>
                <select id="divisions-filter" name="divisions" multiple>
                    <option value="Food Grocery" selected>Food Grocery</option>
                    <option value="NonFood Grocery">NonFood Grocery</option>
                    <option value="Fresh_Froz_Food">Fresh_Froz_Food</option>
                    <option value="Hardlines">Hardlines</option>
                    <option value="CH Hardlines">CH Hardlines</option>
                    <option value="Centralized Hardlines">Centralized Hardlines</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label for="departments-filter">Select Departments:</label>
                <select id="departments-filter" name="departments" multiple>
                    <!-- Will be populated based on division selection -->
                </select>
            </div>
            
            <button class="btn btn-secondary" 
                    hx-get="/api/replenishment-stats"
                    hx-target="#section-replenishment"
                    hx-include="[name='own_brand_filter'], #period-select, #divisions-filter, #departments-filter"
                    hx-vals='{"level": "{{ level }}"}'>
                Apply Filters
            </button>
        </div>
    </div>
    {% endif %}
    
    <div class="chart-container">
        <div class="chart-section">
            <div class="chart-wrapper">
                {% if chart_json %}
                <div id="replenishment-heatmap" class="plotly-chart"></div>
                {% else %}
                <div class="no-data-message">
                    <p>No data available with the current filter settings.</p>
                </div>
                {% endif %}
            </div>
            
            <div class="table-section">
                <h4>Detailed Changes</h4>
                <div class="data-table-wrapper">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Division</th>
                                {% if level == "department" %}
                                <th>Department</th>
                                {% endif %}
                                {% for rtype in repl_types %}
                                <th>{{ rtype.upper().replace('_', ' ') }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for row in table_data %}
                            <tr>
                                <td>{{ row.division }}</td>
                                {% if level == "department" and row.department %}
                                <td>{{ row.department }}</td>
                                {% endif %}
                                {% for rtype in repl_types %}
                                <td class="{% if row[rtype] > 0 %}positive{% elif row[rtype] < 0 %}negative{% else %}neutral{% endif %}">
                                    {{ "{:+.1f}".format(row[rtype]) }}%
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    {% if level == "department" %}
    <!-- Country-level details expander -->
    <div class="expander-section">
        <button class="expander-toggle" onclick="toggleExpander('country-repl-details')">
            <span class="expander-icon">▼</span>
            View Country-Level Details
        </button>
        
        <div id="country-repl-details" class="expander-content" style="display: none;">
            {% for country in ["CZ", "HU", "SK"] %}
            <div class="country-section">
                <h4>{{ country }}</h4>
                <div hx-get="/api/replenishment-stats?country={{ country }}&level={{ level }}"
                     hx-trigger="revealed"
                     hx-include="[name='own_brand_filter'], #period-select, #divisions-filter, #departments-filter">
                    <div class="loading-placeholder">
                        <div class="spinner-small"></div>
                        <span>Loading {{ country }} data...</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<script>
    // Render the Plotly heatmap
    {% if chart_json %}
    const heatmapData = {{ chart_json|safe }};
    Plotly.newPlot('replenishment-heatmap', heatmapData.data, heatmapData.layout, {
        responsive: true,
        displayModeBar: false
    });
    {% endif %}
    
    function toggleExpander(id) {
        const content = document.getElementById(id);
        const toggle = content.previousElementSibling;
        const icon = toggle.querySelector('.expander-icon');
        
        if (content.style.display === 'none') {
            content.style.display = 'block';
            icon.textContent = '▲';
            // Trigger HTMX load for country details
            htmx.trigger(content, 'revealed');
        } else {
            content.style.display = 'none';
            icon.textContent = '▼';
        }
    }
</script>
