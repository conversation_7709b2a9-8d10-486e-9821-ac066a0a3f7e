"""
Simple startup script for the basic FastAPI dashboard
"""

import sys
import os
from pathlib import Path

def main():
    """Run the simple dashboard"""
    
    print("🚀 Starting Simple FastAPI Dashboard...")
    
    # Check if data directory exists
    data_dir = Path("data")
    if not data_dir.exists():
        print("❌ Data directory not found!")
        print("Please ensure the 'data' directory exists with parquet files.")
        return
    
    # Check for data files
    data_files = list(data_dir.glob("CE_JDA_SRD_for_streamlit*"))
    if not data_files:
        print("❌ No data files found!")
        print("Please ensure parquet files are in the 'data' directory.")
        return
    
    print(f"✅ Found {len(data_files)} data files")
    
    # Try to import required modules
    try:
        import uvicorn
        import fastapi
        import pandas
        print("✅ All required modules available")
    except ImportError as e:
        print(f"❌ Missing required module: {e}")
        print("Please install: pip install fastapi uvicorn pandas pyarrow")
        return
    
    print("\n📱 Dashboard will be available at: http://127.0.0.1:8000")
    print("📚 API docs will be available at: http://127.0.0.1:8000/docs")
    print("🔍 Health check at: http://127.0.0.1:8000/health")
    print("\n⏹️  Press Ctrl+C to stop the server\n")
    
    try:
        import uvicorn
        uvicorn.run(
            "simple_main:app",
            host="127.0.0.1",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Dashboard stopped!")
    except Exception as e:
        print(f"\n❌ Error starting dashboard: {e}")
        print("\nTroubleshooting:")
        print("1. Install requirements: pip install fastapi uvicorn pandas pyarrow")
        print("2. Check data directory exists with parquet files")
        print("3. Ensure no other application is using port 8000")

if __name__ == "__main__":
    main()
